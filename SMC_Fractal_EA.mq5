//+------------------------------------------------------------------+
//|                                                SMC_Fractal_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SMC Fractal EA - Smart Money Concepts Fractal Structure Trading"

//--- Input parameters
input group "=== Trading Settings ==="
input double   RiskPercent = 2.0;           // Risk percentage per trade
input double   TakeProfitRatio = 2.0;       // Take Profit ratio (multiplier of stop loss)
input int      MagicNumber = 123456;        // Magic number for trades
input string   TradeComment = "SMC_Fractal"; // Trade comment

input group "=== Fractal Settings ==="
input int      MinCandlesForSwing = 3;      // Minimum candles to form swing
input bool     ShowRangeLines = true;       // Show range lines on chart
input bool     ShowBOSSignals = true;       // Show BOS/CHoCH signals
input color    RangeHighColor = clrRed;     // Range high line color
input color    RangeLowColor = clrBlue;     // Range low line color
input color    BOSColor = clrYellow;        // BOS signal color

input group "=== Risk Management ==="
input bool     UseTrailingStop = false;    // Use trailing stop
input double   TrailingStopPips = 20;      // Trailing stop distance in pips
input int      MaxOpenTrades = 1;          // Maximum open trades

//--- Global variables
double g_rangeHigh = 0;
double g_rangeLow = 0;
bool g_rangeActive = false;
int g_swingHighBar = -1;
int g_swingLowBar = -1;
datetime g_lastBOSTime = 0;
string g_rangeHighLineName = "SMC_RangeHigh";
string g_rangeLowLineName = "SMC_RangeLow";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("SMC Fractal EA initialized");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up objects
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);
   
   // Clean up BOS arrows
   for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, "BOS_") == 0)
         ObjectDelete(0, objName);
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update fractal structure
   UpdateFractalStructure();
   
   // Check for BOS and trading opportunities
   CheckForBOS();
   
   // Manage existing trades
   ManageTrades();
}

//+------------------------------------------------------------------+
//| Update fractal structure                                         |
//+------------------------------------------------------------------+
void UpdateFractalStructure()
{
   int bars = iBars(_Symbol, _Period);
   if(bars < MinCandlesForSwing * 2) return;
   
   // Find swing high
   int swingHighBar = FindSwingHigh();
   if(swingHighBar == -1) return;
   
   // Find swing low from swing high position
   int swingLowBar = FindSwingLow(swingHighBar);
   if(swingLowBar == -1) return;
   
   // Update range if we have valid swing points
   if(swingHighBar != g_swingHighBar || swingLowBar != g_swingLowBar)
   {
      g_swingHighBar = swingHighBar;
      g_swingLowBar = swingLowBar;
      g_rangeHigh = iHigh(_Symbol, _Period, swingHighBar);
      g_rangeLow = iLow(_Symbol, _Period, swingLowBar);
      g_rangeActive = true;
      
      // Draw range lines
      if(ShowRangeLines)
         DrawRangeLines();
   }
}

//+------------------------------------------------------------------+
//| Find swing high                                                  |
//+------------------------------------------------------------------+
int FindSwingHigh()
{
   int bars = iBars(_Symbol, _Period);
   
   for(int i = MinCandlesForSwing; i < bars - MinCandlesForSwing; i++)
   {
      bool isSwingHigh = true;
      double currentHigh = iHigh(_Symbol, _Period, i);
      
      // Check if previous candles have lower highs
      for(int j = 1; j <= MinCandlesForSwing; j++)
      {
         if(iHigh(_Symbol, _Period, i + j) >= currentHigh)
         {
            isSwingHigh = false;
            break;
         }
      }
      
      // Check if next candles have lower highs
      if(isSwingHigh)
      {
         for(int j = 1; j <= MinCandlesForSwing; j++)
         {
            if(iHigh(_Symbol, _Period, i - j) >= currentHigh)
            {
               isSwingHigh = false;
               break;
            }
         }
      }
      
      if(isSwingHigh)
         return i;
   }
   
   return -1;
}

//+------------------------------------------------------------------+
//| Find swing low from given position                              |
//+------------------------------------------------------------------+
int FindSwingLow(int fromBar)
{
   int bars = iBars(_Symbol, _Period);
   
   for(int i = fromBar - MinCandlesForSwing; i >= MinCandlesForSwing; i--)
   {
      bool isSwingLow = true;
      double currentLow = iLow(_Symbol, _Period, i);
      
      // Check if previous candles have higher lows
      for(int j = 1; j <= MinCandlesForSwing; j++)
      {
         if(iLow(_Symbol, _Period, i + j) <= currentLow)
         {
            isSwingLow = false;
            break;
         }
      }
      
      // Check if next candles have higher lows
      if(isSwingLow)
      {
         for(int j = 1; j <= MinCandlesForSwing; j++)
         {
            if(i - j >= 0 && iLow(_Symbol, _Period, i - j) <= currentLow)
            {
               isSwingLow = false;
               break;
            }
         }
      }
      
      if(isSwingLow)
         return i;
   }
   
   return -1;
}

//+------------------------------------------------------------------+
//| Check for Break of Structure                                    |
//+------------------------------------------------------------------+
void CheckForBOS()
{
   if(!g_rangeActive) return;
   
   double currentClose = iClose(_Symbol, _Period, 0);
   double currentHigh = iHigh(_Symbol, _Period, 0);
   double currentLow = iLow(_Symbol, _Period, 0);
   
   // Check for bullish BOS (break above range high)
   if(currentClose > g_rangeHigh)
   {
      if(ShowBOSSignals)
         CreateBOSSignal(true, 0);
      
      // Consider buy trade
      if(CountOpenTrades() < MaxOpenTrades)
         OpenBuyTrade();
      
      // Update range after BOS
      UpdateRangeAfterBOS();
   }
   // Check for bearish BOS (break below range low)
   else if(currentClose < g_rangeLow)
   {
      if(ShowBOSSignals)
         CreateBOSSignal(false, 0);
      
      // Consider sell trade
      if(CountOpenTrades() < MaxOpenTrades)
         OpenSellTrade();
      
      // Update range after BOS
      UpdateRangeAfterBOS();
   }
}

//+------------------------------------------------------------------+
//| Create BOS signal on chart                                      |
//+------------------------------------------------------------------+
void CreateBOSSignal(bool isBullish, int barIndex)
{
   string objName = "BOS_" + TimeToString(iTime(_Symbol, _Period, barIndex));
   
   ObjectCreate(0, objName, OBJ_ARROW, 0, iTime(_Symbol, _Period, barIndex), 
                isBullish ? iHigh(_Symbol, _Period, barIndex) : iLow(_Symbol, _Period, barIndex));
   ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, isBullish ? 233 : 234);
   ObjectSetInteger(0, objName, OBJPROP_COLOR, BOSColor);
   ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
}

//+------------------------------------------------------------------+
//| Update range after BOS                                          |
//+------------------------------------------------------------------+
void UpdateRangeAfterBOS()
{
   // Reset range to find new structure
   g_rangeActive = false;
   g_swingHighBar = -1;
   g_swingLowBar = -1;
   
   // Remove old range lines
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);
}

//+------------------------------------------------------------------+
//| Draw range lines                                                |
//+------------------------------------------------------------------+
void DrawRangeLines()
{
   // Remove existing lines
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);
   
   // Create range high line
   ObjectCreate(0, g_rangeHighLineName, OBJ_HLINE, 0, 0, g_rangeHigh);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_COLOR, RangeHighColor);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_WIDTH, 2);
   
   // Create range low line
   ObjectCreate(0, g_rangeLowLineName, OBJ_HLINE, 0, 0, g_rangeLow);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_COLOR, RangeLowColor);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_WIDTH, 2);
}

//+------------------------------------------------------------------+
//| Open buy trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double stopLoss = g_rangeLow;
   double takeProfit = ask + (ask - stopLoss) * TakeProfitRatio;

   double lotSize = CalculateLotSize(ask - stopLoss);

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = ORDER_TYPE_BUY;
   request.price = ask;
   request.sl = stopLoss;
   request.tp = takeProfit;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.type_filling = ORDER_FILLING_IOC;

   if(OrderSend(request, result))
   {
      Print("Buy order opened successfully. Ticket: ", result.order);
   }
   else
   {
      Print("Failed to open buy order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double stopLoss = g_rangeHigh;
   double takeProfit = bid - (stopLoss - bid) * TakeProfitRatio;

   double lotSize = CalculateLotSize(stopLoss - bid);

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = ORDER_TYPE_SELL;
   request.price = bid;
   request.sl = stopLoss;
   request.tp = takeProfit;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.type_filling = ORDER_FILLING_IOC;

   if(OrderSend(request, result))
   {
      Print("Sell order opened successfully. Ticket: ", result.order);
   }
   else
   {
      Print("Failed to open sell order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossDistance)
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;

   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double pointValue = tickValue * (SymbolInfoDouble(_Symbol, SYMBOL_POINT) / tickSize);

   double lotSize = riskAmount / (stopLossDistance / SymbolInfoDouble(_Symbol, SYMBOL_POINT) * pointValue);

   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Count open trades                                               |
//+------------------------------------------------------------------+
int CountOpenTrades()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                          |
//+------------------------------------------------------------------+
void ManageTrades()
{
   if(!UseTrailingStop) return;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentSL = PositionGetDouble(POSITION_SL);

         double trailingDistance = TrailingStopPips * SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10;

         if(type == POSITION_TYPE_BUY)
         {
            double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            double newSL = bid - trailingDistance;

            if(newSL > currentSL && newSL < bid)
            {
               ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            }
         }
         else if(type == POSITION_TYPE_SELL)
         {
            double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double newSL = ask + trailingDistance;

            if((currentSL == 0 || newSL < currentSL) && newSL > ask)
            {
               ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Modify position                                                 |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = sl;
   request.tp = tp;

   return OrderSend(request, result);
}
