//+------------------------------------------------------------------+
//|                                                SMC_Fractal_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "SMC Fractal EA - Smart Money Concepts Fractal Structure Trading"

//--- Input parameters
input group "=== Trading Settings ==="
input double   RiskPercent = 2.0;           // Risk percentage per trade
input int      MagicNumber = 123456;        // Magic number for trades
input string   TradeComment = "SMC_Fractal"; // Trade comment

input group "=== Fibonacci Entry Settings ==="
input double   FibEntryLevel = 0.786;       // Fibonacci entry level (0.236, 0.382, 0.5, 0.618, 0.786)
input double   FibSLBuffer = 0.1;           // SL buffer beyond 100% Fib (0.05 = 5%)
input bool     UseLimitOrders = true;       // Use limit orders for Fib entries
input int      OrderExpirationHours = 24;   // Limit order expiration (hours)
input bool     ShowFibLines = true;         // Show Fibonacci lines on chart

input group "=== Fractal Settings ==="
input int      MinCandlesForSwing = 3;      // Minimum candles to form swing
input bool     ShowRangeLines = true;       // Show range lines on chart
input bool     ShowBOSSignals = true;       // Show BOS/CHoCH signals
input color    RangeHighColor = clrRed;     // Range high line color
input color    RangeLowColor = clrBlue;     // Range low line color
input color    BOSColor = clrYellow;        // BOS signal color
input color    FibLineColor = clrGold;      // Fibonacci lines color

input group "=== Intelligent Trailing ==="
input bool     UseIntelligentTrailing = true; // Use intelligent trailing stop
input double   TrailingActivationFib = 0.382; // Start trailing at Fib level (0.382 = 38.2% to TP)
input double   TrailingStepFib = 0.236;     // Trailing step as Fib level
input bool     TrailToBreakeven = true;     // Move to breakeven when profitable
input double   BreakevenTriggerFib = 0.5;   // Trigger BE at Fib level (0.5 = 50% to TP)

input group "=== Risk Management ==="
input int      MaxOpenTrades = 1;          // Maximum open trades

//--- Global variables
double g_rangeHigh = 0;
double g_rangeLow = 0;
bool g_rangeActive = false;
int g_swingHighBar = -1;
int g_swingLowBar = -1;
datetime g_lastBOSTime = 0;
string g_rangeHighLineName = "SMC_RangeHigh";
string g_rangeLowLineName = "SMC_RangeLow";

//--- Fibonacci variables
double g_fibHigh = 0;
double g_fibLow = 0;
bool g_fibActive = false;
bool g_waitingForFibEntry = false;
bool g_isBullishBOS = false;
string g_fibLinesPrefix = "SMC_Fib_";

//--- Pending order tracking
ulong g_pendingOrderTicket = 0;

//--- SMC Structure variables
double g_lastSwingHigh = 0;
double g_lastSwingLow = 0;
bool g_bosConfirmed = false;
bool g_waitingForPullback = false;
datetime g_bosTime = 0;

//--- FVG tracking
struct FVG
{
   double high;
   double low;
   int startBar;
   bool isBullish;
   bool isValid;
};
FVG g_currentFVG;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("SMC Fractal EA initialized");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up objects
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);

   // Clean up BOS arrows
   for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, "BOS_") == 0 || StringFind(objName, g_fibLinesPrefix) == 0)
         ObjectDelete(0, objName);
   }

   // Cancel pending orders
   if(g_pendingOrderTicket > 0)
   {
      MqlTradeRequest request = {};
      MqlTradeResult result = {};
      request.action = TRADE_ACTION_REMOVE;
      request.order = g_pendingOrderTicket;
      OrderSend(request, result);
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update fractal structure
   UpdateFractalStructure();

   // Check for BOS and setup Fibonacci
   CheckForBOS();

   // Check pending orders and Fibonacci entries
   CheckPendingOrders();

   // Manage existing trades with intelligent trailing
   ManageTrades();
}

//+------------------------------------------------------------------+
//| Update fractal structure - Simple approach like your trades     |
//+------------------------------------------------------------------+
void UpdateFractalStructure()
{
   int bars = iBars(_Symbol, _Period);
   if(bars < 20) return;

   // Find most recent significant swing high and low
   int swingHighBar = FindRecentSwingHigh();
   int swingLowBar = FindRecentSwingLow();

   if(swingHighBar == -1 || swingLowBar == -1) return;

   double newRangeHigh = iHigh(_Symbol, _Period, swingHighBar);
   double newRangeLow = iLow(_Symbol, _Period, swingLowBar);

   // Only update if we have a significant range (minimum 20 pips)
   double minRange = 20 * SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10;
   if(newRangeHigh - newRangeLow < minRange) return;

   // Update range if it's new or significantly different
   if(!g_rangeActive ||
      MathAbs(newRangeHigh - g_rangeHigh) > minRange/2 ||
      MathAbs(newRangeLow - g_rangeLow) > minRange/2)
   {
      g_swingHighBar = swingHighBar;
      g_swingLowBar = swingLowBar;
      g_rangeHigh = newRangeHigh;
      g_rangeLow = newRangeLow;
      g_rangeActive = true;
      g_bosConfirmed = false; // Reset BOS status for new range

      // Draw range lines
      if(ShowRangeLines)
         DrawRangeLines();

      Print("New range identified: High=", g_rangeHigh, " Low=", g_rangeLow);
   }
}

//+------------------------------------------------------------------+
//| Find recent swing high - Simple approach                        |
//+------------------------------------------------------------------+
int FindRecentSwingHigh()
{
   int bars = iBars(_Symbol, _Period);
   int lookback = MathMin(50, bars - 5); // Look back 50 candles max

   double highestHigh = 0;
   int highestBar = -1;

   // Find the highest high in recent candles
   for(int i = 3; i < lookback; i++)
   {
      double currentHigh = iHigh(_Symbol, _Period, i);

      // Simple swing high: higher than 2 candles on each side
      bool isSwingHigh = true;

      // Check 2 candles to the left
      for(int j = 1; j <= 2; j++)
      {
         if(iHigh(_Symbol, _Period, i + j) >= currentHigh)
         {
            isSwingHigh = false;
            break;
         }
      }

      // Check 2 candles to the right
      if(isSwingHigh)
      {
         for(int j = 1; j <= 2; j++)
         {
            if(iHigh(_Symbol, _Period, i - j) >= currentHigh)
            {
               isSwingHigh = false;
               break;
            }
         }
      }

      // If it's a swing high and higher than what we found, use it
      if(isSwingHigh && currentHigh > highestHigh)
      {
         highestHigh = currentHigh;
         highestBar = i;
      }
   }

   return highestBar;
}

//+------------------------------------------------------------------+
//| Find recent swing low - Simple approach                         |
//+------------------------------------------------------------------+
int FindRecentSwingLow()
{
   int bars = iBars(_Symbol, _Period);
   int lookback = MathMin(50, bars - 5); // Look back 50 candles max

   double lowestLow = 999999;
   int lowestBar = -1;

   // Find the lowest low in recent candles
   for(int i = 3; i < lookback; i++)
   {
      double currentLow = iLow(_Symbol, _Period, i);

      // Simple swing low: lower than 2 candles on each side
      bool isSwingLow = true;

      // Check 2 candles to the left
      for(int j = 1; j <= 2; j++)
      {
         if(iLow(_Symbol, _Period, i + j) <= currentLow)
         {
            isSwingLow = false;
            break;
         }
      }

      // Check 2 candles to the right
      if(isSwingLow)
      {
         for(int j = 1; j <= 2; j++)
         {
            if(iLow(_Symbol, _Period, i - j) <= currentLow)
            {
               isSwingLow = false;
               break;
            }
         }
      }

      // If it's a swing low and lower than what we found, use it
      if(isSwingLow && currentLow < lowestLow)
      {
         lowestLow = currentLow;
         lowestBar = i;
      }
   }

   return lowestBar;
}

//+------------------------------------------------------------------+
//| Check for Break of Structure - Simple like your trades         |
//+------------------------------------------------------------------+
void CheckForBOS()
{
   if(!g_rangeActive) return;

   double currentClose = iClose(_Symbol, _Period, 1); // Use previous closed candle
   double currentHigh = iHigh(_Symbol, _Period, 1);
   double currentLow = iLow(_Symbol, _Period, 1);

   // Check for bullish BOS (break above range high)
   if(!g_bosConfirmed && currentClose > g_rangeHigh)
   {
      if(ShowBOSSignals)
         CreateBOSSignal(true, 1);

      g_bosConfirmed = true;
      g_isBullishBOS = true;
      g_bosTime = iTime(_Symbol, _Period, 1);
      g_lastSwingHigh = g_rangeHigh;
      g_lastSwingLow = g_rangeLow;
      g_waitingForPullback = true;

      Print("Bullish BOS confirmed at: ", g_rangeHigh, " - waiting for pullback");
   }
   // Check for bearish BOS (break below range low)
   else if(!g_bosConfirmed && currentClose < g_rangeLow)
   {
      if(ShowBOSSignals)
         CreateBOSSignal(false, 1);

      g_bosConfirmed = true;
      g_isBullishBOS = false;
      g_bosTime = iTime(_Symbol, _Period, 1);
      g_lastSwingHigh = g_rangeHigh;
      g_lastSwingLow = g_rangeLow;
      g_waitingForPullback = true;

      Print("Bearish BOS confirmed at: ", g_rangeLow, " - waiting for pullback");
   }

   // Check for pullback entry after BOS
   if(g_waitingForPullback)
   {
      CheckForPullbackEntry();
   }
}

//+------------------------------------------------------------------+
//| Create BOS signal on chart                                      |
//+------------------------------------------------------------------+
void CreateBOSSignal(bool isBullish, int barIndex)
{
   string objName = "BOS_" + TimeToString(iTime(_Symbol, _Period, barIndex));
   
   ObjectCreate(0, objName, OBJ_ARROW, 0, iTime(_Symbol, _Period, barIndex), 
                isBullish ? iHigh(_Symbol, _Period, barIndex) : iLow(_Symbol, _Period, barIndex));
   ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, isBullish ? 233 : 234);
   ObjectSetInteger(0, objName, OBJPROP_COLOR, BOSColor);
   ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
}

//+------------------------------------------------------------------+
//| Update range after BOS                                          |
//+------------------------------------------------------------------+
void UpdateRangeAfterBOS()
{
   // Reset range to find new structure after pullback trade
   g_rangeActive = false;
   g_swingHighBar = -1;
   g_swingLowBar = -1;
   g_bosConfirmed = false;
   g_waitingForPullback = false;
   g_currentFVG.isValid = false;

   // Remove old range lines
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);
}

//+------------------------------------------------------------------+
//| Detect Fair Value Gap (FVG) during BOS move                    |
//+------------------------------------------------------------------+
void DetectFVG(bool isBullishBOS)
{
   // Look for FVG in the last 5 candles during BOS move
   for(int i = 1; i <= 5; i++)
   {
      if(isBullishBOS)
      {
         // Bullish FVG: gap between candle[i+1] high and candle[i-1] low
         double prevHigh = iHigh(_Symbol, _Period, i + 1);
         double nextLow = iLow(_Symbol, _Period, i - 1);

         if(nextLow > prevHigh) // Gap exists
         {
            g_currentFVG.high = nextLow;
            g_currentFVG.low = prevHigh;
            g_currentFVG.startBar = i;
            g_currentFVG.isBullish = true;
            g_currentFVG.isValid = true;

            Print("Bullish FVG detected: ", g_currentFVG.low, " - ", g_currentFVG.high);
            break;
         }
      }
      else
      {
         // Bearish FVG: gap between candle[i+1] low and candle[i-1] high
         double prevLow = iLow(_Symbol, _Period, i + 1);
         double nextHigh = iHigh(_Symbol, _Period, i - 1);

         if(nextHigh < prevLow) // Gap exists
         {
            g_currentFVG.high = prevLow;
            g_currentFVG.low = nextHigh;
            g_currentFVG.startBar = i;
            g_currentFVG.isBullish = false;
            g_currentFVG.isValid = true;

            Print("Bearish FVG detected: ", g_currentFVG.low, " - ", g_currentFVG.high);
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check for pullback entry - Simple retracement                  |
//+------------------------------------------------------------------+
void CheckForPullbackEntry()
{
   double currentClose = iClose(_Symbol, _Period, 1);
   double currentHigh = iHigh(_Symbol, _Period, 0);
   double currentLow = iLow(_Symbol, _Period, 0);

   // Calculate 50% retracement level for simple pullback detection
   double midLevel = (g_lastSwingHigh + g_lastSwingLow) / 2.0;

   bool pullbackDetected = false;

   if(g_isBullishBOS)
   {
      // For bullish BOS, look for pullback towards the broken high or mid-level
      if(currentLow <= g_lastSwingHigh && currentClose > midLevel)
      {
         pullbackDetected = true;
      }
   }
   else
   {
      // For bearish BOS, look for pullback towards the broken low or mid-level
      if(currentHigh >= g_lastSwingLow && currentClose < midLevel)
      {
         pullbackDetected = true;
      }
   }

   if(pullbackDetected && CountOpenTrades() < MaxOpenTrades && !g_waitingForFibEntry)
   {
      // Setup Fibonacci entry on pullback
      SetupFibonacciEntry(g_isBullishBOS);
      g_waitingForPullback = false;

      Print("Pullback detected, setting up Fibonacci entry");
   }
}

//+------------------------------------------------------------------+
//| Draw range lines                                                |
//+------------------------------------------------------------------+
void DrawRangeLines()
{
   // Remove existing lines
   ObjectDelete(0, g_rangeHighLineName);
   ObjectDelete(0, g_rangeLowLineName);
   
   // Create range high line
   ObjectCreate(0, g_rangeHighLineName, OBJ_HLINE, 0, 0, g_rangeHigh);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_COLOR, RangeHighColor);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, g_rangeHighLineName, OBJPROP_WIDTH, 2);
   
   // Create range low line
   ObjectCreate(0, g_rangeLowLineName, OBJ_HLINE, 0, 0, g_rangeLow);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_COLOR, RangeLowColor);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, g_rangeLowLineName, OBJPROP_WIDTH, 2);
}

//+------------------------------------------------------------------+
//| Setup Fibonacci entry after pullback to FVG                    |
//+------------------------------------------------------------------+
void SetupFibonacciEntry(bool isBullish)
{
   g_isBullishBOS = isBullish;

   // Use the original swing points for Fibonacci, not the FVG
   g_fibHigh = g_lastSwingHigh;
   g_fibLow = g_lastSwingLow;
   g_fibActive = true;
   g_waitingForFibEntry = true;

   // Draw Fibonacci lines from swing high to swing low
   if(ShowFibLines)
      DrawFibonacciLines();

   // Place limit order at Fibonacci level within or near FVG
   if(UseLimitOrders)
      PlaceFibonacciLimitOrder();
}

//+------------------------------------------------------------------+
//| Draw Fibonacci lines                                            |
//+------------------------------------------------------------------+
void DrawFibonacciLines()
{
   // Clean up old Fib lines
   for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, g_fibLinesPrefix) == 0)
         ObjectDelete(0, objName);
   }

   double fibLevels[] = {0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0};
   string fibLabels[] = {"0%", "23.6%", "38.2%", "50%", "61.8%", "78.6%", "100%"};

   for(int i = 0; i < ArraySize(fibLevels); i++)
   {
      double price = g_fibLow + (g_fibHigh - g_fibLow) * (1.0 - fibLevels[i]);
      string objName = g_fibLinesPrefix + fibLabels[i];

      ObjectCreate(0, objName, OBJ_HLINE, 0, 0, price);
      ObjectSetInteger(0, objName, OBJPROP_COLOR, FibLineColor);
      ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_DOT);
      ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);

      // Highlight entry level
      if(MathAbs(fibLevels[i] - FibEntryLevel) < 0.001)
      {
         ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_SOLID);
         ObjectSetInteger(0, objName, OBJPROP_WIDTH, 2);
      }
   }
}

//+------------------------------------------------------------------+
//| Place Fibonacci limit order                                     |
//+------------------------------------------------------------------+
void PlaceFibonacciLimitOrder()
{
   double entryPrice = g_fibLow + (g_fibHigh - g_fibLow) * (1.0 - FibEntryLevel);
   double takeProfit = g_isBullishBOS ? g_fibHigh : g_fibLow;  // 0% Fib level
   double stopLoss;

   // Calculate SL beyond 100% Fib level with buffer
   if(g_isBullishBOS)
   {
      double fibRange = g_fibHigh - g_fibLow;
      stopLoss = g_fibLow - (fibRange * FibSLBuffer);
   }
   else
   {
      double fibRange = g_fibHigh - g_fibLow;
      stopLoss = g_fibHigh + (fibRange * FibSLBuffer);
   }

   double lotSize = CalculateLotSize(MathAbs(entryPrice - stopLoss));

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_PENDING;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = g_isBullishBOS ? ORDER_TYPE_BUY_LIMIT : ORDER_TYPE_SELL_LIMIT;
   request.price = entryPrice;
   request.sl = stopLoss;
   request.tp = takeProfit;
   request.magic = MagicNumber;
   request.comment = TradeComment + "_Fib";
   request.type_filling = ORDER_FILLING_IOC;

   // Set expiration
   if(OrderExpirationHours > 0)
   {
      request.type_time = ORDER_TIME_SPECIFIED;
      request.expiration = TimeCurrent() + OrderExpirationHours * 3600;
   }

   if(OrderSend(request, result))
   {
      g_pendingOrderTicket = result.order;
      Print("Fibonacci limit order placed. Ticket: ", result.order, " Entry: ", entryPrice);
   }
   else
   {
      Print("Failed to place Fibonacci limit order. Error: ", GetLastError());
      g_waitingForFibEntry = false;
   }
}

//+------------------------------------------------------------------+
//| Check pending orders status                                     |
//+------------------------------------------------------------------+
void CheckPendingOrders()
{
   if(g_pendingOrderTicket > 0)
   {
      // Check if order still exists
      if(!OrderSelect(g_pendingOrderTicket))
      {
         // Order was executed or cancelled
         g_pendingOrderTicket = 0;
         g_waitingForFibEntry = false;
         g_fibActive = false;

         // Clean up Fib lines
         for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
         {
            string objName = ObjectName(0, i);
            if(StringFind(objName, g_fibLinesPrefix) == 0)
               ObjectDelete(0, objName);
         }
      }
   }
}



//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossDistance)
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;

   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double pointValue = tickValue * (SymbolInfoDouble(_Symbol, SYMBOL_POINT) / tickSize);

   double lotSize = riskAmount / (stopLossDistance / SymbolInfoDouble(_Symbol, SYMBOL_POINT) * pointValue);

   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Count open trades                                               |
//+------------------------------------------------------------------+
int CountOpenTrades()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| Manage existing trades with intelligent trailing                |
//+------------------------------------------------------------------+
void ManageTrades()
{
   if(!UseIntelligentTrailing) return;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentSL = PositionGetDouble(POSITION_SL);
         double currentTP = PositionGetDouble(POSITION_TP);
         double currentPrice = (type == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);

         // Calculate profit progress as percentage to TP
         double totalDistance = MathAbs(currentTP - openPrice);
         double currentDistance = MathAbs(currentPrice - openPrice);
         double profitProgress = (totalDistance > 0) ? currentDistance / totalDistance : 0;

         // Move to breakeven when profitable
         if(TrailToBreakeven && profitProgress >= BreakevenTriggerFib)
         {
            if(type == POSITION_TYPE_BUY && currentSL < openPrice)
            {
               ModifyPosition(ticket, openPrice, currentTP);
               Print("Position ", ticket, " moved to breakeven");
               continue;
            }
            else if(type == POSITION_TYPE_SELL && (currentSL == 0 || currentSL > openPrice))
            {
               ModifyPosition(ticket, openPrice, currentTP);
               Print("Position ", ticket, " moved to breakeven");
               continue;
            }
         }

         // Start intelligent trailing when activation level is reached
         if(profitProgress >= TrailingActivationFib)
         {
            ApplyIntelligentTrailing(ticket, type, openPrice, currentSL, currentTP, currentPrice, profitProgress);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Apply intelligent trailing logic                                |
//+------------------------------------------------------------------+
void ApplyIntelligentTrailing(ulong ticket, ENUM_POSITION_TYPE type, double openPrice,
                             double currentSL, double currentTP, double currentPrice, double profitProgress)
{
   double totalDistance = MathAbs(currentTP - openPrice);
   double trailingStep = totalDistance * TrailingStepFib;

   if(type == POSITION_TYPE_BUY)
   {
      // Calculate new SL based on trailing step
      double newSL = currentPrice - trailingStep;

      // Ensure new SL is better than current SL and above breakeven
      if(newSL > currentSL && newSL > openPrice)
      {
         ModifyPosition(ticket, newSL, currentTP);
         Print("Buy position ", ticket, " trailing SL updated to: ", newSL);
      }
   }
   else if(type == POSITION_TYPE_SELL)
   {
      // Calculate new SL based on trailing step
      double newSL = currentPrice + trailingStep;

      // Ensure new SL is better than current SL and below breakeven
      if((currentSL == 0 || newSL < currentSL) && newSL < openPrice)
      {
         ModifyPosition(ticket, newSL, currentTP);
         Print("Sell position ", ticket, " trailing SL updated to: ", newSL);
      }
   }
}

//+------------------------------------------------------------------+
//| Modify position                                                 |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = sl;
   request.tp = tp;

   return OrderSend(request, result);
}
