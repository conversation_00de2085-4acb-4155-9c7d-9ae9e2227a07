# CRT EA Customization Guide

## Overview
The CRT EA now includes **ALL** customization options from the original Pine Script indicator, allowing you to fine-tune the EA for different currency pairs and market conditions to maximize winrate and gains.

## 🎯 **Key Customization Categories**

### **1. General Configuration**
- **Higher Timeframe**: Choose the HTF for bulky candle detection
  - Recommended: M15→H4, H1→H4, H4→Daily
- **HTF Candle Size**: Big/Normal/Small (ATR multipliers: 2.1/1.6/1.3)
- **Entry Mode**: FVGs or Order Blocks
- **Require Retracement**: Force retracement confirmation

### **2. Fair Value Gap Settings**
- **FVG Detection Sensitivity**: All/Extreme/High/Normal/Low
  - All: 100x multiplier (most sensitive)
  - Extreme: 6x multiplier
  - High: 2x multiplier (default)
  - Normal: 1.5x multiplier
  - Low: 1x multiplier (least sensitive)
- **Zone Invalidation**: Wick or Close penetration
- **Zone Filtering**: Average Range or Volume Threshold
- **FVG Detection**: Same Type or All bar types
- **Combine Zones**: Merge overlapping FVGs
- **Allow Gaps**: Handle gap openings
- **Delete Untouched**: Remove old unused zones

### **3. Order Block Settings**
- **Swing Length**: 3-45 (smaller = more sensitive)
- **OB Invalidation**: Wick or Close penetration
- **Enable/Disable**: Full control over OB detection

### **4. Breaker Block Settings**
- **Enable/Disable**: Control BB detection
- **BB Invalidation**: Wick or Close penetration
- **Show Full Breakers**: Complete breaker visualization

### **5. TP/SL Customization**
- **Method**: Dynamic (ATR-based) or Fixed (percentage)
- **Dynamic Risk Levels**:
  - Highest: 10x ATR multiplier
  - High: 8x ATR multiplier
  - Normal: 6.5x ATR multiplier
  - Low: 5x ATR multiplier
  - Lowest: 3x ATR multiplier
- **Custom Risk Multiplier**: Override with custom value
- **Dynamic Risk:Reward**: Default 0.39 (customizable)
- **Fixed Percentages**: Custom TP/SL percentages

### **6. Advanced Settings**
- **Max ATR Multiplier**: Maximum OB size filter
- **Extend Zones**: Zone extension length
- **Dynamic Extension**: Adaptive zone sizing
- **Volume Analysis**: Threshold-based filtering
- **Historic Zones**: Show/hide invalidated zones

## 🔧 **Pair-Specific Optimization**

### **High Volatility Pairs (GBP/JPY, GBP/USD)**
```
HTF Candle Size: Big
FVG Sensitivity: Normal or Low
Dynamic Risk: High or Highest
Swing Length: 15-20
Max ATR Mult: 4.0-5.0
```

### **Low Volatility Pairs (EUR/CHF, USD/CHF)**
```
HTF Candle Size: Normal or Small
FVG Sensitivity: High or Extreme
Dynamic Risk: Normal or Low
Swing Length: 8-12
Max ATR Mult: 2.5-3.5
```

### **Major Pairs (EUR/USD, USD/JPY)**
```
HTF Candle Size: Normal
FVG Sensitivity: High
Dynamic Risk: Normal
Swing Length: 10
Max ATR Mult: 3.5
```

### **Exotic Pairs**
```
HTF Candle Size: Big
FVG Sensitivity: Low
Dynamic Risk: Highest
Swing Length: 20-25
Allow Gaps: true
```

## 📊 **Timeframe-Specific Settings**

### **M5 Charts**
```
Higher TF: M30 or H1
HTF Candle Size: Normal
FVG Sensitivity: High
Delete Untouched After: 100 bars
```

### **M15 Charts**
```
Higher TF: H4
HTF Candle Size: Big
FVG Sensitivity: High
Delete Untouched After: 150 bars
```

### **H1 Charts**
```
Higher TF: H4 or Daily
HTF Candle Size: Big
FVG Sensitivity: Normal
Delete Untouched After: 200 bars
```

## 🎯 **Strategy Variations**

### **Conservative Setup (High Winrate)**
```
Entry Mode: FVGs
Require Retracement: true
FVG Sensitivity: Normal
Dynamic Risk: Normal
Zone Invalidation: Close
```

### **Aggressive Setup (More Trades)**
```
Entry Mode: Order Blocks
Require Retracement: false
FVG Sensitivity: High
Dynamic Risk: High
Zone Invalidation: Wick
```

### **Scalping Setup**
```
HTF Candle Size: Small
FVG Sensitivity: Extreme
Swing Length: 5-8
Dynamic Risk: Low
Fixed TP: 0.2%, SL: 0.3%
```

### **Swing Trading Setup**
```
HTF Candle Size: Big
FVG Sensitivity: Low
Dynamic Risk: Highest
Dynamic RR: 0.5-0.8
```

## 🔍 **Market Condition Adaptations**

### **Trending Markets**
```
FVG Sensitivity: High
Require Retracement: false
Dynamic RR: 0.5+
Show Invalidated: false
```

### **Ranging Markets**
```
FVG Sensitivity: Normal
Require Retracement: true
Dynamic RR: 0.3
Combine Zones: true
```

### **High Impact News**
```
HTF Candle Size: Big
Allow Gaps: true
Dynamic Risk: Highest
Max ATR Mult: 5.0+
```

## 📈 **Optimization Process**

### **Step 1: Backtest Base Settings**
1. Start with default settings
2. Run 3-6 months backtest
3. Note winrate and profit factor

### **Step 2: Sensitivity Adjustment**
1. Test different FVG sensitivities
2. Adjust swing length
3. Compare results

### **Step 3: Risk Optimization**
1. Test different risk levels
2. Adjust dynamic RR ratio
3. Find optimal risk/reward balance

### **Step 4: Fine-Tuning**
1. Adjust zone invalidation methods
2. Test retracement requirements
3. Optimize zone management

### **Step 5: Validation**
1. Forward test on demo
2. Monitor live performance
3. Adjust based on market changes

## ⚠️ **Important Notes**

1. **Different pairs need different settings**
2. **Market conditions change - adapt settings accordingly**
3. **Always backtest before live trading**
4. **Start conservative, then optimize**
5. **Monitor performance regularly**
6. **Keep detailed records of what works**

## 🎛️ **Quick Setup Templates**

Save these as .set files in MT5:

**Template 1: EUR/USD M15**
```
InpHigherTF = PERIOD_H4
InpBulkyCandleATRStr = "Big"
InpFVGSensitivityText = "High"
InpSwingLength = 10
InpRiskAmount = "Normal"
```

**Template 2: GBP/JPY H1**
```
InpHigherTF = PERIOD_H4
InpBulkyCandleATRStr = "Big"
InpFVGSensitivityText = "Normal"
InpSwingLength = 15
InpRiskAmount = "High"
```

**Template 3: Scalping M5**
```
InpHigherTF = PERIOD_M30
InpBulkyCandleATRStr = "Normal"
InpFVGSensitivityText = "Extreme"
InpSwingLength = 6
InpTPSLMethod = "Fixed"
InpTPPercent = 0.2
InpSLPercent = 0.3
```

This comprehensive customization system allows you to optimize the CRT EA for any trading pair and market condition, just like the original indicator!
