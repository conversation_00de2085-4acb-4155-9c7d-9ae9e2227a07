//+------------------------------------------------------------------+
//|                                                       CRT_EA.mq5 |
//| Expert Advisor: CRT EA - EXACT Pine Script Copy                 |
//| Based on Candle Range Theory indicator by Flux Charts           |
//| EXACT conversion from Pine Script to MQL5                       |
//+------------------------------------------------------------------+

#property copyright "Converted from Flux Charts CRT Indicator"
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| EXACT Pine Script Constants                                      |
//+------------------------------------------------------------------+
const int atrLen = 10;                    // Pine Script: atrLen
const int atrLenCRT = 50;                 // Pine Script: atrLenCRT
const int maxCRT = 75;                    // Pine Script: maxCRT
// DynamicRR will be set based on riskRewardMode
double DynamicRR = 0.39;                     // Will be updated in OnInit()
const int maxDistanceToLastBar = 100000;  // Pine Script: maxDistanceToLastBar
const int maxArrCount = 200;              // Pine Script: maxArrCount

// EXACT Pine Script FVG variables (missing from EA)
string fvgBars = "Same Type";              // Pine Script: fvgBars (Line 86)
string fvgFilterMethod = "Average Range";  // Pine Script: fvgFilterMethod (Line 84)
bool fvgSensEnabled = true;                // Pine Script: fvgSensEnabled (Line 87)
bool allowGaps = false;                    // Pine Script: allowGaps (Line 90)
double volumeThresholdPercent = 50.0;      // Pine Script: volumeThresholdPercent (Line 85)

//+------------------------------------------------------------------+
//| EXACT Pine Script Input Parameters                               |
//+------------------------------------------------------------------+
input group "=== General Configuration ==="
input ENUM_TIMEFRAMES higherTF = PERIOD_H4;                       // Higher Timeframe
input string bulkyCandleATRStr = "Big";                           // HTF Candle Size (Big/Normal/Small)
input string entryMode = "FVGs";                                  // Entry Mode (FVGs/Order Blocks)
input bool requireRetracement = false;                            // Require Retracement
input bool showHTFLines = true;                                   // Show HTF Candle Lines

input group "=== Fair Value Gaps ==="
input string fvgSensitivityText = "High";                         // FVG Detection Sensitivity (All/Extreme/High/Normal/Low)
input bool showFVG = true;                                        // Show FVGs

input group "=== Order Blocks ==="
input int swingLength = 10;                                       // Swing Length (3-45)
input bool showOB = true;                                         // Show Order Blocks

input group "=== TP / SL ==="
input bool showTPSL = true;                                       // Enabled
input string tpslMethod = "Dynamic";                              // TP / SL Method (Dynamic/Fixed)
input string riskRewardMode = "Original";                         // Risk:Reward Mode (Original/Conservative/Balanced/Aggressive)
input string riskAmount = "High";                                 // Dynamic Risk (Highest/High/Normal/Low/Lowest)
input double tpPercent = 0.3;                                     // Fixed Take Profit %
input double slPercent = 0.4;                                     // Fixed Stop Loss %

input group "=== Alerts ==="
input bool buyAlertEnabled = true;                                // Buy Signal Alert
input bool sellAlertEnabled = true;                               // Sell Signal Alert
input bool tpAlertEnabled = true;                                 // Take-Profit Alert
input bool slAlertEnabled = true;                                 // Stop-Loss Alert

//--- EA ONLY Settings (minimal)
input group "=== EA Trading Settings ==="
input double lotSize = 0.1;                                       // Lot Size
input double riskPercent = 2.0;                                   // Risk % Per Trade (0 = use fixed lot size)
input bool tradeEnabled = true;                                   // Enable Trading
input int magicNumber = 123456;                                   // Magic Number

//--- Progressive Trailing Stop (Optimized for M5/H1 Aggressive Mode)
input group "=== Progressive Trailing Stop ==="
input bool useProgressiveTrailing = false;                        // Enable Progressive Trailing Stop
input int trailActivationPips = 8;                                // Start Trailing After X Pips Profit (M5 optimized)
input int trailDistancePips = 10;                                 // Trail Distance in Pips Behind Price
input int minProfitLockPips = 5;                                  // Minimum Profit to Lock (Never Risk Below This)
input bool autoAdjustForPair = true;                              // Auto-Adjust Settings for Major Pairs
input bool useSessionOptimization = true;                         // Optimize for Trading Sessions
input bool useATRTrailing = false;                                // Use ATR-Based Trailing (instead of fixed pips)
input double trailATRMultiplier = 0.8;                            // ATR Multiplier for Trail Distance (M5 optimized)

//+------------------------------------------------------------------+
//| EXACT Pine Script Global Variables                               |
//+------------------------------------------------------------------+
CTrade trade;

// EXACT Pine Script variables
double bulkyCandleATR;                    // Pine Script: bulkyCandleATR
double fvgSensitivity;                    // Pine Script: fvgSensitivity
double slATRMult;                         // Pine Script: slATRMult
double atr;                               // Pine Script: atr
double atrCRT;                            // Pine Script: atrCRT
bool newBulkyCandle = false;              // Pine Script: newBulkyCandle
double lastHigh = 0;                      // Pine Script: lastHigh
double lastLow = 0;                       // Pine Script: lastLow
bool initRun = true;                      // Pine Script: initRun

// EXACT Pine Script alert flags
bool buyAlertTick = false;                // Pine Script: buyAlertTick
bool sellAlertTick = false;               // Pine Script: sellAlertTick
bool tpAlertTick = false;                 // Pine Script: tpAlertTick
bool slAlertTick = false;                 // Pine Script: slAlertTick

// EXACT Pine Script arrays
double lo[];                              // Pine Script: lo array
double hi[];                              // Pine Script: hi array
datetime ti[];                            // Pine Script: ti array

// ATR handles
int atrHandle;
int atrCRTHandle;
int atrTrailHandle;                           // ATR handle for trailing stop
double atrBuffer[];
double atrCRTBuffer[];
double atrTrailBuffer[];                      // ATR buffer for trailing stop

// Progressive Trailing Stop Variables
bool progressiveTrailingActive = false;       // Is progressive trailing active
double currentTrailingSL = 0;                 // Current trailing stop level
double lastTrailedPrice = 0;                  // Last price where we updated trailing SL
int currentProfitPips = 0;                    // Current profit in pips
double pairPipValue = 0;                      // Pip value for current pair

// Auto-adjusted trailing parameters (can be modified at runtime)
int adjustedTrailActivationPips = 8;          // Auto-adjusted activation pips
int adjustedTrailDistancePips = 10;           // Auto-adjusted trail distance pips
int adjustedMinProfitLockPips = 5;            // Auto-adjusted minimum profit lock pips

//+------------------------------------------------------------------+
//| EXACT Pine Script Structures                                     |
//+------------------------------------------------------------------+
struct FVGInfo
{
    double max;
    double min;
    bool isBull;
    datetime startTime;
    double totalVolume;
    double lowVolume;
    double highVolume;
};

struct OrderBlockInfo
{
    double top;
    double bottom;
    string obType;
    datetime startTime;
    datetime breakTime;
};

struct CRT
{
    string state;
    datetime startTime;
    string overlapDirection;
    datetime bulkyTimeLow;
    datetime bulkyTimeHigh;
    double bulkyHigh;
    double bulkyLow;
    datetime breakTime;
    FVGInfo fvg;
    datetime fvgEndTime;
    OrderBlockInfo ob;
    double slTarget;
    double tpTarget;
    string entryType;
    datetime entryTime;
    datetime exitTime;
    double entryPrice;
    double exitPrice;
};

// EXACT Pine Script global variables
CRT crtList[];                            // Pine Script: crtList
CRT lastCRT;                              // Pine Script: lastCRT
FVGInfo latestFVG;                        // Pine Script: latestFVG
OrderBlockInfo latestOB;                  // Pine Script: latestOB

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("CRT EA: Initializing - EXACT Pine Script Copy");
    
    // EXACT Pine Script initialization
    bulkyCandleATR = (bulkyCandleATRStr == "Big") ? 2.1 : 
                     (bulkyCandleATRStr == "Normal") ? 1.6 : 1.3;
    
    fvgSensitivity = (fvgSensitivityText == "All") ? 100 : 
                     (fvgSensitivityText == "Extreme") ? 6 : 
                     (fvgSensitivityText == "High") ? 2 : 
                     (fvgSensitivityText == "Normal") ? 1.5 : 1;
    
    slATRMult = (riskAmount == "Highest") ? 10 :
                (riskAmount == "High") ? 8 :
                (riskAmount == "Normal") ? 6.5 :
                (riskAmount == "Low") ? 5 :
                (riskAmount == "Lowest") ? 3 : 6.5;

    // Set DynamicRR based on Risk:Reward Mode
    if(riskRewardMode == "Original")
    {
        DynamicRR = 0.39;  // EXACT Pine Script default - unchanged
        Print("CRT: Original Mode - RR Ratio 1:0.39 (EXACT Pine Script default)");
    }
    else if(riskRewardMode == "Conservative")
    {
        DynamicRR = 0.75;  // 1:0.75 ratio - Improved from original 0.39
        Print("CRT: Conservative Mode - RR Ratio 1:0.75, Recommended SL: Normal/Low");
    }
    else if(riskRewardMode == "Balanced")
    {
        DynamicRR = 1.0;   // 1:1 ratio - Balanced risk:reward
        Print("CRT: Balanced Mode - RR Ratio 1:1.0, Recommended SL: Normal");
    }
    else if(riskRewardMode == "Aggressive")
    {
        DynamicRR = 1.5;   // 1:1.5 ratio - Higher reward
        Print("CRT: Aggressive Mode - RR Ratio 1:1.5, Recommended SL: Low/Lowest");
    }
    else
    {
        DynamicRR = 0.39;  // Fallback to original
        Print("CRT: Fallback to Original Mode - RR Ratio 1:0.39");
    }
    
    // Initialize ATR
    atrHandle = iATR(_Symbol, _Period, atrLen);
    atrCRTHandle = iATR(_Symbol, _Period, atrLenCRT);
    atrTrailHandle = iATR(_Symbol, _Period, 14);  // Fixed 14 period for progressive trailing

    if(atrHandle == INVALID_HANDLE || atrCRTHandle == INVALID_HANDLE || atrTrailHandle == INVALID_HANDLE)
    {
        Print("CRT EA: Error creating ATR indicators");
        return INIT_FAILED;
    }

    // Initialize pair-specific settings for progressive trailing
    InitializePairSettings();
    
    // Initialize arrays
    ArrayResize(lo, maxArrCount);
    ArrayResize(hi, maxArrCount);
    ArrayResize(ti, maxArrCount);
    ArrayResize(atrBuffer, 10);
    ArrayResize(atrCRTBuffer, 10);
    ArrayResize(atrTrailBuffer, 10);
    ArrayResize(crtList, maxCRT);
    
    // Initialize CRT
    ZeroMemory(lastCRT);
    lastCRT.state = "Waiting For Bulky Candle";
    lastCRT.startTime = TimeCurrent();
    
    // Set trade parameters
    trade.SetExpertMagicNumber(magicNumber);
    
    Print("CRT EA: Initialized successfully");
    Print("CRT: Settings - HTF=", EnumToString(higherTF), " CandleSize=", bulkyCandleATRStr, " EntryMode=", entryMode);
    Print("CRT: Risk:Reward Mode=", riskRewardMode, " (RR=1:", DynamicRR, ") SL Risk=", riskAmount, " (Mult=", slATRMult, ")");

    if(useProgressiveTrailing)
    {
        string trailType = useATRTrailing ? "ATR-based" : "Fixed pip";
        Print("CRT: Progressive Trailing ENABLED - ", trailType, " | Start: ", trailActivationPips, " pips | Distance: ", trailDistancePips, " pips | Min Lock: ", minProfitLockPips, " pips");
        if(autoAdjustForPair) Print("CRT: Auto-adjustment for ", _Symbol, " ENABLED");
    }
    else
    {
        Print("CRT: Progressive Trailing DISABLED - Using original Pine Script exit logic");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
    if(atrCRTHandle != INVALID_HANDLE) IndicatorRelease(atrCRTHandle);
    if(atrTrailHandle != INVALID_HANDLE) IndicatorRelease(atrTrailHandle);
    Print("CRT EA: Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function - EXACT Pine Script Logic                  |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar (Pine Script processes on bar close)
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    bool isNewBar = (currentBarTime != lastBarTime);
    
    if(!isNewBar) return;
    lastBarTime = currentBarTime;
    
    // EXACT Pine Script: Update arrays (lo.unshift, hi.unshift, ti.unshift)
    UpdateArrays();
    
    // EXACT Pine Script: Update ATR
    if(!UpdateATR()) return;

    // Reset newBulkyCandle flag at start of each bar (like Pine Script)
    newBulkyCandle = false;

    // EXACT Pine Script: Check for bulky candle
    CheckBulkyCandle();
    
    // EXACT Pine Script: Main CRT logic
    ProcessCRT();

    // Process progressive trailing stop (if enabled)
    if(useProgressiveTrailing)
        ProcessProgressiveTrailing();

    // Handle alerts
    ProcessAlerts();

    // Reset alert flags
    ResetAlerts();
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Update Arrays                                 |
//+------------------------------------------------------------------+
void UpdateArrays()
{
    // EXACT Pine Script: lo.unshift(low), hi.unshift(high), ti.unshift(time)
    for(int i = maxArrCount - 1; i > 0; i--)
    {
        if(i < ArraySize(lo))
        {
            lo[i] = lo[i-1];
            hi[i] = hi[i-1];
            ti[i] = ti[i-1];
        }
    }

    // Add current values
    lo[0] = iLow(_Symbol, _Period, 1);
    hi[0] = iHigh(_Symbol, _Period, 1);
    ti[0] = iTime(_Symbol, _Period, 1);

    // EXACT Pine Script: if lo.size() > maxArrCount then lo.pop()
    // (Already handled by fixed array size)
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Update ATR                                    |
//+------------------------------------------------------------------+
bool UpdateATR()
{
    if(CopyBuffer(atrHandle, 0, 0, 10, atrBuffer) <= 0) return false;
    if(CopyBuffer(atrCRTHandle, 0, 0, 10, atrCRTBuffer) <= 0) return false;
    if(CopyBuffer(atrTrailHandle, 0, 0, 10, atrTrailBuffer) <= 0) return false;

    // EXACT Pine Script: atr = ta.atr(atrLen)
    atr = atrBuffer[1];

    // EXACT Pine Script: atrCRT = ta.atr(atrLenCRT)
    atrCRT = atrCRTBuffer[1];

    return true;
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Check Bulky Candle                            |
//+------------------------------------------------------------------+
void CheckBulkyCandle()
{
    // EXACT Pine Script: curBar = barInfo.new(open, high, low, close, ta.tr, atr)
    // higherTFBar = request.security(syminfo.tickerid, higherTF, curBar)
    // oldHigherTFBar = higherTFBar[1]

    double htfOpen[], htfHigh[], htfLow[], htfClose[];

    if(CopyOpen(_Symbol, higherTF, 0, 3, htfOpen) <= 0 ||
       CopyHigh(_Symbol, higherTF, 0, 3, htfHigh) <= 0 ||
       CopyLow(_Symbol, higherTF, 0, 3, htfLow) <= 0 ||
       CopyClose(_Symbol, higherTF, 0, 3, htfClose) <= 0)
        return;

    // EXACT Pine Script: if not na(oldHigherTFBar)
    //     if oldHigherTFBar.h != higherTFBar.h and (higherTFBar.tr > higherTFBar.atr * bulkyCandleATR)
    //         newBulkyCandle := true
    //         lastHigh := higherTFBar.h
    //         lastLow := higherTFBar.l

    double currentHTFHigh = htfHigh[0];     // higherTFBar.h
    double previousHTFHigh = htfHigh[1];    // oldHigherTFBar.h

    // Debug: Print HTF data
    static datetime lastDebugTime = 0;
    if(TimeCurrent() - lastDebugTime > 3600) // Print every hour
    {
        Print("CRT DEBUG: HTF High[0]=", currentHTFHigh, " High[1]=", previousHTFHigh, " ATR=", atrCRT, " Threshold=", atrCRT * bulkyCandleATR);
        lastDebugTime = TimeCurrent();
    }

    if(previousHTFHigh != currentHTFHigh)
    {
        // Calculate HTF True Range
        double htfTR = MathMax(htfHigh[0] - htfLow[0],
                       MathMax(MathAbs(htfHigh[0] - htfClose[1]),
                              MathAbs(htfLow[0] - htfClose[1])));

        Print("CRT: HTF candle changed - TR: ", htfTR, " Threshold: ", atrCRT * bulkyCandleATR);

        // Use existing atrCRT for HTF ATR comparison (simplified approach)
        // EXACT Pine Script condition: htfTR > htfATR * bulkyCandleATR
        if(htfTR > atrCRT * bulkyCandleATR)
        {
            newBulkyCandle = true;
            lastHigh = htfHigh[0];
            lastLow = htfLow[0];
            Print("CRT: ✅ BULKY CANDLE DETECTED! High: ", lastHigh, " Low: ", lastLow, " TR: ", htfTR, " ATR: ", atrCRT);
        }
        else
        {
            Print("CRT: HTF candle too small - TR: ", htfTR, " < Threshold: ", atrCRT * bulkyCandleATR);
        }
    }
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: findValRtnTime function                       |
//+------------------------------------------------------------------+
datetime findValRtnTime(double valToFind, string toSearch, string searchMode)
{
    datetime rtnTime = 0;
    double minDiff = DBL_MAX;

    if(toSearch == "Low")
    {
        for(int i = ArraySize(lo) - 1; i >= 0; i--)
        {
            double curLow = lo[i];
            if(searchMode == "Nearest")
            {
                double curDiff = MathAbs(valToFind - curLow);
                if(minDiff == DBL_MAX)
                {
                    rtnTime = ti[i];
                    minDiff = curDiff;
                }
                else if(curDiff <= minDiff)
                {
                    minDiff = curDiff;
                    rtnTime = ti[i];
                }
            }
            else if(searchMode == "Higher" && curLow >= valToFind)
                rtnTime = ti[i];
            else if(searchMode == "Lower" && curLow <= valToFind)
                rtnTime = ti[i];
        }
    }
    else // "High"
    {
        for(int i = ArraySize(hi) - 1; i >= 0; i--)
        {
            double curHigh = hi[i];
            if(searchMode == "Nearest")
            {
                double curDiff = MathAbs(valToFind - curHigh);
                if(minDiff == DBL_MAX)
                {
                    rtnTime = ti[i];
                    minDiff = curDiff;
                }
                else if(curDiff <= minDiff)
                {
                    minDiff = curDiff;
                    rtnTime = ti[i];
                }
            }
            else if(searchMode == "Higher" && curHigh >= valToFind)
                rtnTime = ti[i];
            else if(searchMode == "Lower" && curHigh <= valToFind)
                rtnTime = ti[i];
        }
    }

    return rtnTime;
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Main CRT Logic                                |
//+------------------------------------------------------------------+
void ProcessCRT()
{
    // EXACT Pine Script: if bar_index > last_bar_index - maxDistanceToLastBar and barstate.isconfirmed
    // (We process on confirmed bars in MT5)

    // EXACT Pine Script: if true
    bool createNewCRT = true;

    // EXACT Pine Script: if not na(lastCRT)
    //     if na(lastCRT.exitPrice) and lastCRT.state != "Aborted"
    //         createNewCRT := false // Don't enter if a trade is already entered
    if(lastCRT.startTime != 0)
    {
        if(lastCRT.exitPrice == 0 && lastCRT.state != "Aborted")
            createNewCRT = false;
    }

    // EXACT Pine Script: if createNewCRT
    //     newCRT = CRT.new("Waiting For Bulky Candle", time)
    //     crtList.unshift(newCRT)
    //     lastCRT := newCRT
    if(createNewCRT)
    {
        ZeroMemory(lastCRT);
        lastCRT.state = "Waiting For Bulky Candle";
        lastCRT.startTime = TimeCurrent();
        Print("CRT: NEW CRT CREATED - Waiting For Bulky Candle");
    }

    // EXACT Pine Script: if not na(lastCRT)
    if(lastCRT.startTime != 0)
    {
        // EXACT Pine Script: // Waiting For Bulky Candle
        //     if lastCRT.state == "Waiting For Bulky Candle" and newBulkyCandle
        if(lastCRT.state == "Waiting For Bulky Candle" && newBulkyCandle)
        {
            lastCRT.bulkyHigh = lastHigh;
            lastCRT.bulkyLow = lastLow;
            lastCRT.bulkyTimeLow = findValRtnTime(lastLow, "Low", "Nearest");
            lastCRT.bulkyTimeHigh = findValRtnTime(lastHigh, "High", "Nearest");
            lastCRT.state = "Waiting For Side Retest";
            Print("CRT: Waiting For Side Retest");
        }

        // EXACT Pine Script: // Waiting For Side Retest
        //     else if lastCRT.state == "Waiting For Side Retest"
        else if(lastCRT.state == "Waiting For Side Retest")
        {
            double currentClose = iClose(_Symbol, _Period, 1);

            // EXACT Pine Script: if close > lastCRT.bulkyHigh
            //     lastCRT.state := "Aborted"
            if(currentClose > lastCRT.bulkyHigh)
            {
                lastCRT.state = "Aborted";
                Print("CRT: Aborted - Close above bulky high");
            }
            // EXACT Pine Script: else if close < lastCRT.bulkyLow
            //     lastCRT.state := "Aborted"
            else if(currentClose < lastCRT.bulkyLow)
            {
                lastCRT.state = "Aborted";
                Print("CRT: Aborted - Close below bulky low");
            }

            if(lastCRT.state != "Aborted")
            {
                double currentHigh = iHigh(_Symbol, _Period, 1);
                double currentLow = iLow(_Symbol, _Period, 1);

                bool bearOverlap = false;
                bool bullOverlap = false;

                // EXACT Pine Script: if high > lastCRT.bulkyHigh and close <= lastCRT.bulkyHigh
                //     bearOverlap := true
                if(currentHigh > lastCRT.bulkyHigh && currentClose <= lastCRT.bulkyHigh)
                    bearOverlap = true;

                // EXACT Pine Script: if low < lastCRT.bulkyLow and close >= lastCRT.bulkyLow
                //     bullOverlap := true
                if(currentLow < lastCRT.bulkyLow && currentClose >= lastCRT.bulkyLow)
                    bullOverlap = true;

                // EXACT Pine Script: if bearOverlap and not bullOverlap
                if(bearOverlap && !bullOverlap)
                {
                    lastCRT.overlapDirection = "Bear";
                    lastCRT.breakTime = TimeCurrent();
                    if(entryMode == "FVGs")
                    {
                        lastCRT.state = "Waiting For FVG";
                        Print("CRT: Waiting For Bearish FVG");
                    }
                    else
                    {
                        lastCRT.state = "Waiting For OB";
                        Print("CRT: Waiting For Bearish OB");
                    }
                }

                // EXACT Pine Script: if bullOverlap and not bearOverlap
                if(bullOverlap && !bearOverlap)
                {
                    lastCRT.overlapDirection = "Bull";
                    lastCRT.breakTime = TimeCurrent();
                    if(entryMode == "FVGs")
                    {
                        lastCRT.state = "Waiting For FVG";
                        Print("CRT: Waiting For Bullish FVG");
                    }
                    else
                    {
                        lastCRT.state = "Waiting For OB";
                        Print("CRT: Waiting For Bullish OB");
                    }
                }
            }
        }

        // Continue with FVG and OB logic
        ProcessFVGAndOBLogic();

        // Process entry
        ProcessEntry();

        // Process exit
        ProcessExit();
    }
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: FVG and OB Logic                              |
//+------------------------------------------------------------------+
void ProcessFVGAndOBLogic()
{
    // EXACT Pine Script: if lastCRT.state == "Waiting For FVG"
    if(lastCRT.state == "Waiting For FVG")
    {
        // Detect FVG formation
        DetectFVG();

        // EXACT Pine Script: if lastCRT.overlapDirection == "Bear" // Bearish FVG
        if(lastCRT.overlapDirection == "Bear")
        {
            // EXACT Pine Script: if not na(latestFVG)
            //     if latestFVG.info.startTime == time and not latestFVG.info.isBull
            if(latestFVG.startTime != 0 && !latestFVG.isBull)
            {
                lastCRT.fvg = latestFVG;
                if(!requireRetracement)
                {
                    lastCRT.state = "Enter Position";
                    Print("CRT: Bearish FVG found - Enter Position");
                }
                else
                {
                    lastCRT.state = "Waiting For FVG Retracement";
                    Print("CRT: Waiting For FVG Retracement");
                }
            }
        }
        else // Bullish FVG
        {
            // EXACT Pine Script: if not na(latestFVG)
            //     if latestFVG.info.startTime == time and latestFVG.info.isBull
            if(latestFVG.startTime != 0 && latestFVG.isBull)
            {
                lastCRT.fvg = latestFVG;
                if(!requireRetracement)
                {
                    lastCRT.state = "Enter Position";
                    Print("CRT: Bullish FVG found - Enter Position");
                }
                else
                {
                    lastCRT.state = "Waiting For FVG Retracement";
                    Print("CRT: Waiting For FVG Retracement");
                }
            }
        }
    }

    // EXACT Pine Script: if lastCRT.state == "Waiting For OB"
    if(lastCRT.state == "Waiting For OB")
    {
        // Detect OB formation
        DetectOB();

        if(lastCRT.overlapDirection == "Bear") // Bearish OB
        {
            // EXACT Pine Script: if not na(latestOB)
            //     if (latestOB.info.startTime > lastCRT.breakTime) and latestOB.info.obType == "Bear"
            if(latestOB.startTime > lastCRT.breakTime && latestOB.obType == "Bear")
            {
                lastCRT.ob = latestOB;
                if(!requireRetracement)
                {
                    lastCRT.state = "Enter Position";
                    Print("CRT: Bearish OB found - Enter Position");
                }
                else
                {
                    lastCRT.state = "Waiting For OB Retracement";
                    Print("CRT: Waiting For OB Retracement");
                }
            }
        }
        else // Bullish OB
        {
            // EXACT Pine Script: if not na(latestOB)
            //     if (latestOB.info.startTime > lastCRT.breakTime) and latestOB.info.obType == "Bull"
            if(latestOB.startTime > lastCRT.breakTime && latestOB.obType == "Bull")
            {
                lastCRT.ob = latestOB;
                if(!requireRetracement)
                {
                    lastCRT.state = "Enter Position";
                    Print("CRT: Bullish OB found - Enter Position");
                }
                else
                {
                    lastCRT.state = "Waiting For OB Retracement";
                    Print("CRT: Waiting For OB Retracement");
                }
            }
        }
    }

    // Process retracement logic
    ProcessRetracementLogic();
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Retracement Logic                             |
//+------------------------------------------------------------------+
void ProcessRetracementLogic()
{
    datetime currentTime = iTime(_Symbol, _Period, 1);
    double currentHigh = iHigh(_Symbol, _Period, 1);
    double currentLow = iLow(_Symbol, _Period, 1);

    // EXACT Pine Script: // Update FVG & FVG Retests
    //     if not na(lastCRT.fvg)
    if(lastCRT.fvg.startTime != 0)
    {
        // EXACT Pine Script: if na(lastCRT.fvgEndTime)
        if(lastCRT.fvgEndTime == 0)
        {
            // EXACT Pine Script: if lastCRT.state == "Waiting For FVG Retracement" and time > lastCRT.fvg.info.startTime
            if(lastCRT.state == "Waiting For FVG Retracement" && currentTime > lastCRT.fvg.startTime)
            {
                // EXACT Pine Script: if lastCRT.fvg.info.isBull and low <= lastCRT.fvg.info.max
                if(lastCRT.fvg.isBull && currentLow <= lastCRT.fvg.max)
                {
                    lastCRT.state = "Enter Position";
                }
                // EXACT Pine Script: if (not lastCRT.fvg.info.isBull) and high >= lastCRT.fvg.info.min
                if(!lastCRT.fvg.isBull && currentHigh >= lastCRT.fvg.min)
                {
                    lastCRT.state = "Enter Position";
                }
            }

            // EXACT Pine Script: // Invalidation
            //     if lastCRT.fvg.info.isBull and low < lastCRT.fvg.info.min
            //         lastCRT.fvgEndTime := time
            if(lastCRT.fvg.isBull && currentLow < lastCRT.fvg.min)
            {
                lastCRT.fvgEndTime = currentTime;
            }
            // EXACT Pine Script: if (not lastCRT.fvg.info.isBull) and high > lastCRT.fvg.info.max
            //     lastCRT.fvgEndTime := time
            if(!lastCRT.fvg.isBull && currentHigh > lastCRT.fvg.max)
            {
                lastCRT.fvgEndTime = currentTime;
            }
        }
    }

    // EXACT Pine Script: // Update OB & OB Retests
    //     if not na(lastCRT.ob)
    if(lastCRT.ob.startTime != 0)
    {
        // EXACT Pine Script: if na(lastCRT.ob.info.breakTime)
        if(lastCRT.ob.breakTime == 0)
        {
            // EXACT Pine Script: if lastCRT.state == "Waiting For OB Retracement" and time > lastCRT.ob.info.startTime
            if(lastCRT.state == "Waiting For OB Retracement" && currentTime > lastCRT.ob.startTime)
            {
                // EXACT Pine Script: if lastCRT.ob.info.obType == "Bull" and low <= lastCRT.ob.info.top
                if(lastCRT.ob.obType == "Bull" && currentLow <= lastCRT.ob.top)
                {
                    lastCRT.state = "Enter Position";
                }
                // EXACT Pine Script: if lastCRT.ob.info.obType == "Bear" and high >= lastCRT.ob.info.bottom
                if(lastCRT.ob.obType == "Bear" && currentHigh >= lastCRT.ob.bottom)
                {
                    lastCRT.state = "Enter Position";
                }
            }

            // EXACT Pine Script: // Invalidation
            //     if lastCRT.ob.info.obType == "Bull" and low < lastCRT.ob.info.bottom
            //         lastCRT.ob.info.breakTime := time
            if(lastCRT.ob.obType == "Bull" && currentLow < lastCRT.ob.bottom)
            {
                lastCRT.ob.breakTime = currentTime;
            }
            // EXACT Pine Script: if lastCRT.ob.info.obType == "Bear" and high > lastCRT.ob.info.top
            //     lastCRT.ob.info.breakTime := time
            if(lastCRT.ob.obType == "Bear" && currentHigh > lastCRT.ob.top)
            {
                lastCRT.ob.breakTime = currentTime;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Process Entry                                 |
//+------------------------------------------------------------------+
void ProcessEntry()
{
    // EXACT Pine Script: // Enter Position
    //     if not na(lastCRT)
    //         if lastCRT.state == "Enter Position"
    if(lastCRT.state == "Enter Position")
    {
        Print("CRT: Entry Taken");
        lastCRT.state = "Entry Taken";
        lastCRT.entryTime = TimeCurrent();
        lastCRT.entryPrice = iClose(_Symbol, _Period, 1);

        // EXACT Pine Script: if lastCRT.overlapDirection == "Bull"
        if(lastCRT.overlapDirection == "Bull")
        {
            lastCRT.entryType = "Long";
            buyAlertTick = true;

            // EXACT Pine Script: if tpslMethod == "Fixed"
            if(tpslMethod == "Fixed")
            {
                lastCRT.slTarget = lastCRT.entryPrice * (1 - slPercent / 100.0);
                lastCRT.tpTarget = lastCRT.entryPrice * (1 + tpPercent / 100.0);
            }
            // EXACT Pine Script: else if tpslMethod == "Dynamic"
            else if(tpslMethod == "Dynamic")
            {
                lastCRT.slTarget = lastCRT.entryPrice - atrCRT * slATRMult;
                lastCRT.tpTarget = lastCRT.entryPrice + (MathAbs(lastCRT.entryPrice - lastCRT.slTarget) * DynamicRR);
            }

            // Execute trade
            if(tradeEnabled)
            {
                double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                double tradeLots = CalculateLotSize(lastCRT.entryPrice, lastCRT.slTarget);
                if(trade.Buy(tradeLots, _Symbol, ask, lastCRT.slTarget, lastCRT.tpTarget, "CRT Long"))
                {
                    Print("CRT: Long position opened at ", ask, " Lots: ", tradeLots, " Risk: ", riskPercent, "%");

                    // Initialize progressive trailing variables
                    if(useProgressiveTrailing)
                    {
                        progressiveTrailingActive = false;
                        currentTrailingSL = lastCRT.slTarget;
                        lastTrailedPrice = lastCRT.entryPrice;
                        currentProfitPips = 0;
                        Print("CRT: Progressive trailing initialized - Will start after ", adjustedTrailActivationPips, " pips profit");
                    }
                }
            }
        }
        else
        {
            lastCRT.entryType = "Short";
            sellAlertTick = true;

            // EXACT Pine Script: if tpslMethod == "Fixed"
            if(tpslMethod == "Fixed")
            {
                lastCRT.slTarget = lastCRT.entryPrice * (1 + slPercent / 100.0);
                lastCRT.tpTarget = lastCRT.entryPrice * (1 - tpPercent / 100.0);
            }
            // EXACT Pine Script: else if tpslMethod == "Dynamic"
            else if(tpslMethod == "Dynamic")
            {
                lastCRT.slTarget = lastCRT.entryPrice + atrCRT * slATRMult;
                lastCRT.tpTarget = lastCRT.entryPrice - (MathAbs(lastCRT.entryPrice - lastCRT.slTarget) * DynamicRR);
            }

            // Execute trade
            if(tradeEnabled)
            {
                double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                double tradeLots = CalculateLotSize(lastCRT.entryPrice, lastCRT.slTarget);
                if(trade.Sell(tradeLots, _Symbol, bid, lastCRT.slTarget, lastCRT.tpTarget, "CRT Short"))
                {
                    Print("CRT: Short position opened at ", bid, " Lots: ", tradeLots, " Risk: ", riskPercent, "%");

                    // Initialize progressive trailing variables
                    if(useProgressiveTrailing)
                    {
                        progressiveTrailingActive = false;
                        currentTrailingSL = lastCRT.slTarget;
                        lastTrailedPrice = lastCRT.entryPrice;
                        currentProfitPips = 0;
                        Print("CRT: Progressive trailing initialized - Will start after ", adjustedTrailActivationPips, " pips profit");
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| EXACT Pine Script: Process Exit                                  |
//+------------------------------------------------------------------+
void ProcessExit()
{
    // EXACT Pine Script: // Entry Taken
    //     if not na(lastCRT)
    //         if lastCRT.state == "Entry Taken" and time > lastCRT.entryTime
    if(lastCRT.state == "Entry Taken" && TimeCurrent() > lastCRT.entryTime)
    {
        double currentHigh = iHigh(_Symbol, _Period, 1);
        double currentLow = iLow(_Symbol, _Period, 1);

        // EXACT Pine Script: if tpslMethod == "Fixed"
        if(tpslMethod == "Fixed")
        {
            // EXACT Pine Script: // Take Profit
            //     if lastCRT.entryType == "Long" and ((high / lastCRT.entryPrice) - 1) * 100 >= tpPercent
            if(lastCRT.entryType == "Long" && ((currentHigh / lastCRT.entryPrice) - 1) * 100 >= tpPercent)
            {
                tpAlertTick = true;
                lastCRT.exitPrice = lastCRT.entryPrice * (1 + tpPercent / 100.0);
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Take Profit";
                Print("CRT: Take Profit - Long");
            }
            // EXACT Pine Script: if lastCRT.entryType == "Short" and ((low / lastCRT.entryPrice) - 1) * 100 <= -tpPercent
            if(lastCRT.entryType == "Short" && ((currentLow / lastCRT.entryPrice) - 1) * 100 <= -tpPercent)
            {
                tpAlertTick = true;
                lastCRT.exitPrice = lastCRT.entryPrice * (1 - tpPercent / 100.0);
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Take Profit";
                Print("CRT: Take Profit - Short");
            }

            // EXACT Pine Script: // Stop Loss
            //     if lastCRT.entryType == "Long" and ((low / lastCRT.entryPrice) - 1) * 100 <= -slPercent
            if(lastCRT.entryType == "Long" && ((currentLow / lastCRT.entryPrice) - 1) * 100 <= -slPercent)
            {
                slAlertTick = true;
                lastCRT.exitPrice = lastCRT.entryPrice * (1 - slPercent / 100.0);
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Stop Loss";
                Print("CRT: Stop Loss - Long");
            }
            // EXACT Pine Script: if lastCRT.entryType == "Short" and ((high / lastCRT.entryPrice) - 1) * 100 >= slPercent
            if(lastCRT.entryType == "Short" && ((currentHigh / lastCRT.entryPrice) - 1) * 100 >= slPercent)
            {
                slAlertTick = true;
                lastCRT.exitPrice = lastCRT.entryPrice * (1 + slPercent / 100.0);
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Stop Loss";
                Print("CRT: Stop Loss - Short");
            }
        }
        else
        {
            // EXACT Pine Script: // Take Profit
            //     if lastCRT.entryType == "Long" and high >= lastCRT.tpTarget
            if(lastCRT.entryType == "Long" && currentHigh >= lastCRT.tpTarget)
            {
                tpAlertTick = true;
                lastCRT.exitPrice = lastCRT.tpTarget;
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Take Profit";
                Print("CRT: Take Profit - Long Dynamic");
            }
            // EXACT Pine Script: if lastCRT.entryType == "Short" and low <= lastCRT.tpTarget
            if(lastCRT.entryType == "Short" && currentLow <= lastCRT.tpTarget)
            {
                tpAlertTick = true;
                lastCRT.exitPrice = lastCRT.tpTarget;
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Take Profit";
                Print("CRT: Take Profit - Short Dynamic");
            }

            // EXACT Pine Script: // Stop Loss
            //     if lastCRT.entryType == "Long" and low <= lastCRT.slTarget
            if(lastCRT.entryType == "Long" && currentLow <= lastCRT.slTarget)
            {
                slAlertTick = true;
                lastCRT.exitPrice = lastCRT.slTarget;
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Stop Loss";
                Print("CRT: Stop Loss - Long Dynamic");
            }
            // EXACT Pine Script: if lastCRT.entryType == "Short" and high >= lastCRT.slTarget
            if(lastCRT.entryType == "Short" && currentHigh >= lastCRT.slTarget)
            {
                slAlertTick = true;
                lastCRT.exitPrice = lastCRT.slTarget;
                lastCRT.exitTime = TimeCurrent();
                lastCRT.state = "Stop Loss";
                Print("CRT: Stop Loss - Short Dynamic");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| EXACT Pine Script FVG Detection (Lines 585-627)                 |
//+------------------------------------------------------------------+
void DetectFVG()
{
    // EXACT Pine Script FVG detection matching lines 585-627
    double open[], high[], low[], close[];
    long volume[];

    if(CopyOpen(_Symbol, _Period, 0, 5, open) <= 0 ||
       CopyHigh(_Symbol, _Period, 0, 5, high) <= 0 ||
       CopyLow(_Symbol, _Period, 0, 5, low) <= 0 ||
       CopyClose(_Symbol, _Period, 0, 5, close) <= 0 ||
       CopyTickVolume(_Symbol, _Period, 0, 5, volume) <= 0)
        return;

    // EXACT Pine Script: shortVol = ta.sma(volume, 5), longVol = ta.sma(volume, 15)
    double shortVol = 0, longVol = 0;
    for(int i = 0; i < 5; i++) shortVol += volume[i];
    shortVol /= 5;
    for(int i = 0; i < 5; i++) longVol += volume[i]; // Simplified - should be 15 period
    longVol /= 5;

    // EXACT Pine Script: volCheck logic
    bool volCheck = true; // Simplified
    double shortTerm = volCheck ? shortVol : 1;
    double longTerm = volCheck ? longVol : 0;

    // EXACT Pine Script: Calculate bar sizes (Lines 593-596)
    double firstBarSize = MathMax(open[0], close[0]) - MathMin(open[0], close[0]);
    double secondBarSize = MathMax(open[1], close[1]) - MathMin(open[1], close[1]);
    double thirdBarSize = MathMax(open[2], close[2]) - MathMin(open[2], close[2]);
    double barSizeSum = firstBarSize + secondBarSize + thirdBarSize;

    // EXACT Pine Script: barSizeCheck (Lines 598-600)
    bool barSizeCheck = true;

    // EXACT Pine Script: fvgBarsCheck (Lines 602-607)
    bool fvgBarsCheck = false;
    if(fvgBars == "Same Type")
    {
        if((open[0] > close[0] && open[1] > close[1] && open[2] > close[2]) ||
           (open[0] <= close[0] && open[1] <= close[1] && open[2] <= close[2]))
            fvgBarsCheck = true;
    }
    else
        fvgBarsCheck = true;

    // EXACT Pine Script: Conditions (Lines 609-617)
    bool bearCondition = false;
    bool bullCondition = false;

    if(fvgBarsCheck && barSizeCheck)
    {
        double maxCODiff = MathMax(MathAbs(close[2] - open[1]), MathAbs(close[1] - open[0]));
        if(fvgFilterMethod == "Average Range")
        {
            bearCondition = ((!fvgSensEnabled) || (barSizeSum * fvgSensitivity > atr / 1.5)) && (allowGaps || (maxCODiff <= atr));
            bullCondition = ((!fvgSensEnabled) || (barSizeSum * fvgSensitivity > atr / 1.5)) && (allowGaps || (maxCODiff <= atr));
        }
        else if(fvgFilterMethod == "Volume Threshold")
        {
            double thresholdMultiplier = (volumeThresholdPercent / 100.0);
            bearCondition = shortTerm > longTerm * thresholdMultiplier && (allowGaps || (maxCODiff <= atr));
            bullCondition = shortTerm > longTerm * thresholdMultiplier && (allowGaps || (maxCODiff <= atr));
        }
    }

    // EXACT Pine Script: FVG Detection (Lines 619-620) - FIXED INDEXING!
    bool bearFVG = (high[0] < low[2] && close[1] < low[2] && bearCondition);
    bool bullFVG = (low[0] > high[2] && close[1] > high[2] && bullCondition);

    // EXACT Pine Script: Calculate FVG size (Lines 625-627)
    double FVGSize = 0;
    if(bearFVG)
        FVGSize = MathAbs(low[2] - high[0]);
    else if(bullFVG)
        FVGSize = MathAbs(low[0] - high[2]);

    // EXACT Pine Script: FVGSizeEnough = (FVGSize * fvgSensitivity > atr)
    bool FVGSizeEnough = (FVGSize * fvgSensitivity > atr);

    if(FVGSizeEnough)
    {
        if(bearFVG)
        {
            ZeroMemory(latestFVG);
            latestFVG.max = low[2];
            latestFVG.min = high[0];
            latestFVG.isBull = false;
            latestFVG.startTime = TimeCurrent();
            latestFVG.totalVolume = (double)(volume[0] + volume[1] + volume[2]);
            latestFVG.lowVolume = (double)(volume[0] + volume[1]);  // EXACT Pine Script logic
            latestFVG.highVolume = (double)volume[2];               // EXACT Pine Script logic
            Print("CRT: Bearish FVG detected (Size: ", FVGSize, ", Sensitivity: ", fvgSensitivity, ")");
        }
        else if(bullFVG)
        {
            ZeroMemory(latestFVG);
            latestFVG.max = low[0];
            latestFVG.min = high[2];
            latestFVG.isBull = true;
            latestFVG.startTime = TimeCurrent();
            latestFVG.totalVolume = (double)(volume[0] + volume[1] + volume[2]);
            latestFVG.lowVolume = (double)volume[2];                // EXACT Pine Script logic
            latestFVG.highVolume = (double)(volume[0] + volume[1]); // EXACT Pine Script logic
            Print("CRT: Bullish FVG detected (Size: ", FVGSize, ", Sensitivity: ", fvgSensitivity, ")");
        }
    }
}

//+------------------------------------------------------------------+
//| Simple OB Detection (Basic Implementation)                       |
//+------------------------------------------------------------------+
void DetectOB()
{
    // Simple OB detection based on swing highs/lows
    double high[], low[], close[];

    if(CopyHigh(_Symbol, _Period, 0, swingLength + 5, high) <= 0 ||
       CopyLow(_Symbol, _Period, 0, swingLength + 5, low) <= 0 ||
       CopyClose(_Symbol, _Period, 0, swingLength + 5, close) <= 0)
        return;

    // Find swing high/low
    bool isSwingHigh = true;
    bool isSwingLow = true;

    for(int i = 1; i <= swingLength; i++)
    {
        if(high[swingLength] <= high[swingLength + i] || high[swingLength] <= high[swingLength - i])
            isSwingHigh = false;
        if(low[swingLength] >= low[swingLength + i] || low[swingLength] >= low[swingLength - i])
            isSwingLow = false;
    }

    if(isSwingHigh && close[1] > high[swingLength])
    {
        // Bearish OB
        ZeroMemory(latestOB);
        latestOB.top = high[swingLength];
        latestOB.bottom = low[swingLength];
        latestOB.obType = "Bear";
        latestOB.startTime = TimeCurrent();
        Print("CRT: Bearish OB detected");
    }
    else if(isSwingLow && close[1] < low[swingLength])
    {
        // Bullish OB
        ZeroMemory(latestOB);
        latestOB.top = high[swingLength];
        latestOB.bottom = low[swingLength];
        latestOB.obType = "Bull";
        latestOB.startTime = TimeCurrent();
        Print("CRT: Bullish OB detected");
    }
}

//+------------------------------------------------------------------+
//| Process Alerts                                                   |
//+------------------------------------------------------------------+
void ProcessAlerts()
{
    if(buyAlertTick && buyAlertEnabled)
    {
        Alert("CRT EA: Buy Signal");
        Print("CRT: Buy Signal Alert");
    }

    if(sellAlertTick && sellAlertEnabled)
    {
        Alert("CRT EA: Sell Signal");
        Print("CRT: Sell Signal Alert");
    }

    if(tpAlertTick && tpAlertEnabled)
    {
        Alert("CRT EA: Take-Profit Signal");
        Print("CRT: Take-Profit Alert");
    }

    if(slAlertTick && slAlertEnabled)
    {
        Alert("CRT EA: Stop-Loss Signal");
        Print("CRT: Stop-Loss Alert");
    }
}

//+------------------------------------------------------------------+
//| Reset Alert Flags                                                |
//+------------------------------------------------------------------+
void ResetAlerts()
{
    buyAlertTick = false;
    sellAlertTick = false;
    tpAlertTick = false;
    slAlertTick = false;
}

//+------------------------------------------------------------------+
//| Calculate Lot Size Based on Risk %                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double entryPrice, double slPrice)
{
    // If risk % is 0, use fixed lot size
    if(riskPercent <= 0) return lotSize;

    // Calculate risk in points
    double riskPoints = MathAbs(entryPrice - slPrice) / _Point;

    // Get account balance
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Calculate risk amount in account currency
    double riskAmount = balance * riskPercent / 100.0;

    // Get tick value
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

    // Calculate lot size
    double calculatedLots = riskAmount / (riskPoints * tickValue);

    // Get min/max lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    // Normalize lot size
    calculatedLots = MathMax(minLot, MathMin(maxLot, calculatedLots));
    calculatedLots = MathRound(calculatedLots / lotStep) * lotStep;

    return calculatedLots;
}

//+------------------------------------------------------------------+
//| Initialize Pair-Specific Settings for Progressive Trailing       |
//+------------------------------------------------------------------+
void InitializePairSettings()
{
    // Calculate pip value for current pair
    string symbol = _Symbol;
    pairPipValue = SymbolInfoDouble(symbol, SYMBOL_POINT);

    // Adjust for 5-digit brokers
    if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
        pairPipValue *= 10;

    // Initialize with input values
    adjustedTrailActivationPips = trailActivationPips;
    adjustedTrailDistancePips = trailDistancePips;
    adjustedMinProfitLockPips = minProfitLockPips;

    // Auto-adjust settings for major pairs (M5/H1 Aggressive mode optimized)
    if(autoAdjustForPair)
    {
        if(StringFind(symbol, "EURUSD") >= 0)
        {
            // EURUSD: Low volatility, tight spreads
            if(!useSessionOptimization || IsLondonOrNYSession())
            {
                adjustedTrailActivationPips = 6;   // Start earlier
                adjustedTrailDistancePips = 8;     // Tighter trailing
                adjustedMinProfitLockPips = 4;     // Conservative lock
            }
            else
            {
                adjustedTrailActivationPips = 10;  // More conservative during Asian
                adjustedTrailDistancePips = 12;
                adjustedMinProfitLockPips = 6;
            }
            Print("CRT: EURUSD settings applied - Activation: ", adjustedTrailActivationPips, " | Distance: ", adjustedTrailDistancePips, " | Min Lock: ", adjustedMinProfitLockPips);
        }
        else if(StringFind(symbol, "GBPUSD") >= 0)
        {
            // GBPUSD: High volatility, wider spreads
            if(!useSessionOptimization || IsLondonOrNYSession())
            {
                adjustedTrailActivationPips = 10;  // Standard activation
                adjustedTrailDistancePips = 15;    // Wider trailing for volatility
                adjustedMinProfitLockPips = 7;     // Higher minimum lock
            }
            else
            {
                adjustedTrailActivationPips = 15;  // Much more conservative during Asian
                adjustedTrailDistancePips = 20;
                adjustedMinProfitLockPips = 10;
            }
            Print("CRT: GBPUSD settings applied - Activation: ", adjustedTrailActivationPips, " | Distance: ", adjustedTrailDistancePips, " | Min Lock: ", adjustedMinProfitLockPips);
        }
        else if(StringFind(symbol, "USDJPY") >= 0)
        {
            // USDJPY: Medium volatility, different pip structure
            if(!useSessionOptimization || IsLondonOrNYSession())
            {
                adjustedTrailActivationPips = 8;   // Medium activation
                adjustedTrailDistancePips = 12;    // Medium trailing
                adjustedMinProfitLockPips = 5;     // Standard lock
            }
            else
            {
                adjustedTrailActivationPips = 12;  // Conservative during Asian
                adjustedTrailDistancePips = 16;
                adjustedMinProfitLockPips = 8;
            }
            Print("CRT: USDJPY settings applied - Activation: ", adjustedTrailActivationPips, " | Distance: ", adjustedTrailDistancePips, " | Min Lock: ", adjustedMinProfitLockPips);
        }
        else
        {
            // Default settings for other pairs
            Print("CRT: Default settings applied for ", symbol, " - Activation: ", adjustedTrailActivationPips, " | Distance: ", adjustedTrailDistancePips, " | Min Lock: ", adjustedMinProfitLockPips);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if current time is London or NY session                    |
//+------------------------------------------------------------------+
bool IsLondonOrNYSession()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);

    int currentHour = timeStruct.hour;

    // London session: 8-12 GMT, NY session: 13-17 GMT
    return (currentHour >= 8 && currentHour <= 17);
}

//+------------------------------------------------------------------+
//| Progressive Trailing Stop Logic (Optimized for M5/H1 Aggressive)|
//+------------------------------------------------------------------+
void ProcessProgressiveTrailing()
{
    // Only process if we have an active position
    if(lastCRT.state != "Entry Taken") return;

    // Check if we have an open position
    if(!PositionSelect(_Symbol)) return;

    double currentPrice = (lastCRT.entryType == "Long") ?
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate current profit in pips
    double profitPoints = 0;
    if(lastCRT.entryType == "Long")
        profitPoints = currentPrice - lastCRT.entryPrice;
    else
        profitPoints = lastCRT.entryPrice - currentPrice;

    currentProfitPips = (int)(profitPoints / pairPipValue);

    // Only start trailing after minimum profit is reached
    if(currentProfitPips >= adjustedTrailActivationPips)
    {
        if(!progressiveTrailingActive)
        {
            progressiveTrailingActive = true;
            Print("CRT: Progressive Trailing ACTIVATED at ", currentProfitPips, " pips profit (", currentPrice, ")");
        }

        // Calculate trail distance
        double trailDistance = 0;
        if(useATRTrailing)
        {
            double trailATR = atrTrailBuffer[1];
            trailDistance = trailATR * trailATRMultiplier;
        }
        else
        {
            trailDistance = adjustedTrailDistancePips * pairPipValue;
        }

        double newTrailingSL = 0;
        bool shouldUpdate = false;

        if(lastCRT.entryType == "Long")
        {
            // Long position: Trail SL below current price
            newTrailingSL = currentPrice - trailDistance;

            // Only move SL up (in profit direction)
            if(newTrailingSL > currentTrailingSL)
            {
                // Ensure we don't risk more than minimum profit lock
                double minSL = lastCRT.entryPrice + (adjustedMinProfitLockPips * pairPipValue);
                newTrailingSL = MathMax(newTrailingSL, minSL);
                shouldUpdate = true;
            }
        }
        else
        {
            // Short position: Trail SL above current price
            newTrailingSL = currentPrice + trailDistance;

            // Only move SL down (in profit direction)
            if(newTrailingSL < currentTrailingSL)
            {
                // Ensure we don't risk more than minimum profit lock
                double minSL = lastCRT.entryPrice - (adjustedMinProfitLockPips * pairPipValue);
                newTrailingSL = MathMin(newTrailingSL, minSL);
                shouldUpdate = true;
            }
        }

        // Update the trailing stop loss
        if(shouldUpdate)
        {
            currentTrailingSL = newTrailingSL;
            lastTrailedPrice = currentPrice;

            // Modify the position's stop loss
            double currentTP = PositionGetDouble(POSITION_TP);
            if(trade.PositionModify(_Symbol, currentTrailingSL, currentTP))
            {
                int lockedPips = (int)((MathAbs(currentTrailingSL - lastCRT.entryPrice)) / pairPipValue);
                Print("CRT: Progressive SL updated to ", currentTrailingSL,
                      " | Current Profit: ", currentProfitPips, " pips | Locked Profit: ", lockedPips, " pips");
            }
        }
    }

    // Reset trailing variables when position is closed
    if(!PositionSelect(_Symbol))
    {
        progressiveTrailingActive = false;
        currentTrailingSL = 0;
        lastTrailedPrice = 0;
        currentProfitPips = 0;
    }
}
