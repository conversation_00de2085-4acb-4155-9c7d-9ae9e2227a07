# CRT EA Backtesting Setup Guide

## 🚀 **Quick Start for Backtesting**

### **Step 1: Compile the EA**
1. Open MetaEditor
2. Open `CRT_EA.mq5`
3. Press F7 to compile
4. Ensure no errors

### **Step 2: Basic Backtest Setup**
1. Open MT5 Strategy Tester (Ctrl+R)
2. Select `CRT_EA.ex5`
3. Choose symbol (e.g., EURUSD)
4. Choose timeframe (M15 recommended)
5. Set date range (at least 3 months)

### **Step 3: Initial Test Settings**
For your first backtest, use these settings:

```
=== General Configuration ===
Higher Timeframe: PERIOD_H4
HTF Candle Size: "Big"
Entry Mode: "FVGs"
Require Retracement: false
Show HTF Lines: true

=== Fair Value Gaps ===
FVG Detection Sensitivity: "High"
Show FVGs: true
FVG Enabled: true
Zone Invalidation: "Close"
Zone Filtering: "Average Range"
FVG Detection: "Same Type"

=== Order Blocks ===
Swing Length: 10
Show Order Blocks: true
OBs Enabled: true
OB Invalidation Method: "Close"

=== TP/SL ===
Enabled: true
TP/SL Method: "Dynamic"
Dynamic Risk: "Normal"
Dynamic Risk:Reward Ratio: 0.39

=== Trading Settings ===
Lot Size: 0.1
Magic Number: 123456
Enable Trading: true
Test Mode: false (for full CRT logic)

=== Risk Management ===
Max Risk Per Trade %: 2.0
Maximum Concurrent Trades: 1
Use Money Management: false
```

### **Step 4: Troubleshooting No Trades**

If you're still getting no trades, try these steps:

#### **Option A: Enable Test Mode**
```
Test Mode: true
```
This will place simple test trades to verify the EA is working.

#### **Option B: Relaxed Settings**
```
FVG Detection Sensitivity: "All"
HTF Candle Size: "Small"
Require Retracement: false
Zone Invalidation: "Wick"
Dynamic Risk: "Low"
```

#### **Option C: Check Logs**
Look for these debug messages in the Experts tab:
- "CRT: New Bulky Candle detected!"
- "CRT: FVG Pattern detected"
- "CRT: Bearish/Bullish FVG created"
- "CRT: Waiting For Side Retest"

### **Step 5: Recommended Backtest Pairs & Timeframes**

#### **High Probability Setups:**
1. **EURUSD M15** with H4 HTF
2. **GBPUSD M15** with H4 HTF  
3. **USDJPY M15** with H4 HTF
4. **AUDUSD M30** with H4 HTF

#### **Timeframe Combinations:**
- M5 → M30 HTF
- M15 → H4 HTF
- M30 → H4 HTF
- H1 → D1 HTF

### **Step 6: Expected Results**

#### **Typical Performance Metrics:**
- **Win Rate**: 60-75%
- **Profit Factor**: 1.5-2.5
- **Trades per Month**: 5-15 (depending on settings)
- **Max Drawdown**: 5-15%

#### **If No Trades Still:**
1. Check if HTF is higher than current TF
2. Ensure enough historical data (3+ months)
3. Try different sensitivity settings
4. Enable Test Mode to verify EA functionality

### **Step 7: Optimization Process**

#### **Phase 1: Basic Validation**
1. Run 3-month backtest with default settings
2. Verify trades are being placed
3. Check win rate and profit factor

#### **Phase 2: Sensitivity Optimization**
1. Test FVG Sensitivity: All → Extreme → High → Normal → Low
2. Test HTF Candle Size: Big → Normal → Small
3. Test Dynamic Risk: Highest → High → Normal → Low → Lowest

#### **Phase 3: Fine-Tuning**
1. Optimize Swing Length (5-20)
2. Test Retracement requirement
3. Adjust Risk:Reward ratio (0.2-0.8)

### **Step 8: Common Issues & Solutions**

#### **Issue: "Not enough bars for analysis"**
- **Solution**: Ensure at least 100 bars of history

#### **Issue: "Failed to update ATR buffers"**
- **Solution**: Check symbol and timeframe validity

#### **Issue: "Higher timeframe must be higher than current"**
- **Solution**: Use H4 for M15 charts, D1 for H1 charts

#### **Issue: No HTF bulky candles detected**
- **Solution**: Lower HTF Candle Size to "Normal" or "Small"

### **Step 9: Debug Mode**

Add this to inputs for detailed debugging:
```cpp
input bool InpDebugMode = true; // Enable Debug Prints
```

This will show:
- HTF candle detection
- FVG formation
- Order block creation
- State transitions
- Entry signals

### **Step 10: Validation Checklist**

Before going live:
- ✅ EA compiles without errors
- ✅ Backtest shows trades being placed
- ✅ Win rate > 60%
- ✅ Profit factor > 1.5
- ✅ Maximum drawdown < 20%
- ✅ Forward test on demo account
- ✅ Settings optimized for specific pair

## 🎯 **Quick Test Command**

For immediate testing, use these minimal settings:
```
Test Mode: true
Enable Trading: true
Lot Size: 0.01
```

This will place simple test trades every 50 bars to verify the EA is working.

## 📊 **Expected Debug Output**

When working correctly, you should see:
```
CRT: First tick processed, EA is now active
CRT: New Bulky Candle detected! HTF High: 1.0950 HTF Low: 1.0920
CRT: New CRT started - Waiting For Bulky Candle
CRT: Waiting For Side Retest
CRT: FVG Pattern detected - Bear: false Bull: true
CRT: Bullish FVG created - Size: 0.0015 ATR: 0.0012
CRT: Waiting For Bullish FVG
CRT: Entry Taken
```

If you see these messages, the EA is working correctly!
