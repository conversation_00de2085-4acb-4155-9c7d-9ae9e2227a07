# CRT EA Documentation

## Overview
The CRT (Candle Range Theory) Expert Advisor is a complete conversion of the Pine Script indicator "Candle Range Theory | Flux Charts" into a fully functional MT5 trading robot. This EA implements every aspect of the original indicator's logic and adds automated trading capabilities.

## Key Features

### 1. Complete Indicator Logic Implementation
- **FVG (Fair Value Gap) Detection**: Exact 3-bar pattern detection with volume and ATR filtering
- **Order Block Detection**: Swing-based order block identification with invalidation rules
- **HTF (Higher Timeframe) Analysis**: Multi-timeframe bulky candle detection
- **CRT State Machine**: Complete state-based trading logic

### 2. Trading Capabilities
- **Automated Entry**: Executes trades based on CRT signals
- **TP/SL Management**: Both Fixed percentage and Dynamic ATR-based methods
- **Position Monitoring**: Real-time trade management and exit conditions
- **Risk Management**: Comprehensive risk controls and money management

### 3. Configuration Options
All original indicator parameters are preserved:
- Higher timeframe selection
- HTF candle size (Big/Normal/Small)
- Entry mode (FVGs or Order Blocks)
- Retracement requirements
- TP/SL methods and risk levels
- FVG sensitivity settings
- Order block swing length

## Trading Logic

### CRT State Machine
The EA follows the exact same state machine as the indicator:

1. **"Waiting For Bulky Candle"**: Monitors HTF for qualifying candles
2. **"Waiting For Side Retest"**: Looks for overlap with bulky candle range
3. **"Waiting For FVG/OB"**: Waits for FVG or Order Block formation
4. **"Waiting For Retracement"**: Optional retracement confirmation
5. **"Enter Position"**: Executes trade with calculated TP/SL
6. **"Entry Taken"**: Monitors position for exit conditions

### Entry Conditions
- **Bullish Setup**: HTF bulky candle → Low retest → Bullish FVG/OB → Entry
- **Bearish Setup**: HTF bulky candle → High retest → Bearish FVG/OB → Entry

### TP/SL Calculation
**Fixed Method**:
- TP: Entry price ± TP percentage
- SL: Entry price ± SL percentage

**Dynamic Method**:
- SL: Entry price ± (ATR × Risk multiplier)
- TP: Entry price ± (SL distance × Risk-Reward ratio of 0.39)

## Input Parameters

### General Configuration
- `InpHigherTF`: Higher timeframe (default: H4)
- `InpBulkyCandleATRStr`: HTF candle size (Big/Normal/Small)
- `InpEntryMode`: Entry mode (FVGs/Order Blocks)
- `InpRequireRetracement`: Require retracement confirmation
- `InpShowHTFLines`: Show HTF candle lines

### Fair Value Gaps
- `InpFVGSensitivityText`: Detection sensitivity (All/Extreme/High/Normal/Low)
- `InpShowFVG`: Show FVGs

### Order Blocks
- `InpSwingLength`: Swing length for OB detection (3-45)
- `InpShowOB`: Show Order Blocks

### TP/SL Settings
- `InpShowTPSL`: Enable TP/SL display
- `InpTPSLMethod`: Method (Dynamic/Fixed)
- `InpRiskAmount`: Dynamic risk level (Highest/High/Normal/Low/Lowest)
- `InpTPPercent`: Fixed TP percentage
- `InpSLPercent`: Fixed SL percentage

### Trading Settings
- `InpLotSize`: Fixed lot size
- `InpMagicNumber`: Magic number for trade identification
- `InpSlippage`: Maximum slippage in points
- `InpTradeEnabled`: Enable/disable actual trading

### Risk Management
- `InpMaxRiskPercent`: Maximum risk per trade
- `InpMaxTrades`: Maximum concurrent trades
- `InpUseMoneyManagement`: Enable automatic lot sizing
- `InpMaxDrawdownPercent`: Maximum drawdown limit
- `InpUseTrailingStop`: Enable trailing stop
- `InpTrailingStopDistance`: Trailing stop distance

### Alerts
- `InpBuyAlertEnabled`: Buy signal alerts
- `InpSellAlertEnabled`: Sell signal alerts
- `InpTPAlertEnabled`: Take profit alerts
- `InpSLAlertEnabled`: Stop loss alerts

## Installation and Setup

1. **Copy Files**: Place `CRT_EA.mq5` in your MT5 `Experts` folder
2. **Compile**: Open in MetaEditor and compile (F7)
3. **Attach to Chart**: Drag EA to desired chart
4. **Configure**: Set input parameters according to your preferences
5. **Enable Trading**: Ensure "Allow automated trading" is enabled

## Recommended Settings

### Conservative Setup
- Higher TF: H4 (for M15 charts)
- HTF Candle Size: Big
- Entry Mode: FVGs
- Require Retracement: true
- TP/SL Method: Dynamic
- Risk Amount: Normal
- Max Risk: 1-2%

### Aggressive Setup
- Higher TF: H1 (for M5 charts)
- HTF Candle Size: Normal
- Entry Mode: Order Blocks
- Require Retracement: false
- TP/SL Method: Fixed
- TP: 0.5%, SL: 0.8%
- Max Risk: 3-5%

## Risk Warnings

1. **Backtesting Required**: Always backtest before live trading
2. **Demo Testing**: Test on demo account first
3. **Risk Management**: Never risk more than you can afford to lose
4. **Market Conditions**: EA performance varies with market conditions
5. **Monitoring**: Regularly monitor EA performance and adjust settings

## Technical Notes

### Code Structure
- **Exact Conversion**: All Pine Script logic converted to MQL5
- **State Machine**: Maintains exact same trading states
- **Data Structures**: Custom structures mirror Pine Script UDTs
- **Calculations**: Identical mathematical formulas

### Performance Considerations
- **Memory Usage**: Optimized array management
- **CPU Usage**: Efficient calculation methods
- **Network**: Minimal broker communication

### Compatibility
- **MT5 Only**: Designed specifically for MetaTrader 5
- **All Symbols**: Works with any tradeable symbol
- **All Timeframes**: Supports all standard timeframes

## Support and Updates

This EA is a faithful conversion of the original Pine Script indicator. All trading logic, calculations, and state management follow the exact same patterns as the source code.

For questions or issues, refer to the original indicator documentation or consult with a qualified MQL5 developer.

## Disclaimer

This EA is for educational and research purposes. Past performance does not guarantee future results. Trading involves substantial risk of loss and is not suitable for all investors.
